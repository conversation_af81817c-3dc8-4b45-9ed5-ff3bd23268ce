service ctf
{
    disable = no
    socket_type = stream
    protocol    = tcp
    wait        = no
    user        = root
    type        = UNLISTED
    port        = 8888
    bind        = 0.0.0.0
    server      = /usr/sbin/chroot
    server_args = --userspec=1000:1000 / timeout 50 /home/<USER>/calculator
    banner_fail = /etc/banner_fail

    # safety options
    per_source	  = 10 # the maximum instances of this service per source IP address
    rlimit_cpu	  = 60 # the maximum number of CPU seconds that the service may use
    rlimit_as     = 1024M # the Address Space resource limit for the service
    #access_times = 2:00-9:00 12:00-24:00

    #Instances   = 20 #process limit
    #per_source  = 5 #link ip limit

    #log warning die
    log_on_success  = PID HOST EXIT DURATION
    log_on_failure  = HOST ATTEMPT
    log_type =FILE /var/log/myservice.log 8388608 15728640

}
