<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="mobile-agent" content="format=html5; url=http://m.zx.wed114.cn/shenghuo/20160920156214.html" />
        <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE7">
		<link href="__PUBLIC__/style/lady.css" type="text/css" rel="stylesheet" />
		<script type='text/javascript' src='__PUBLIC__/style/ismobile.js'></script>
    </head>

    <body>

        {include file="common/header" /}

        <!--顶部通栏-->
        <script src='/jiehun/goto/my-65547.js' language='javascript'></script>

        <div class="position"><a href='{:url('index/index')}'>主页</a> > <a href="{:url('cate/index',array('cateid'=>$cates['id']))}">{$cates.catename}</a> >  </div>

        <div class="overall">
            <div class="left">
                <div class="scrap">
                    <h1>{$articles.title}</h1>
                    <div class="spread">
                        <span class="writor">发布时间：{$articles.time|date="Y-m-d",###}</span>
                        <span class="writor">编辑：{$articles.author}</span>
                        <span class="writor">标签：
                            <?php
                                $arr=explode(',', $articles['keywords']);
                                foreach ($arr as $k=>$v) {
                                    echo "<a href='#'>$v</a>";
                                }
                            ?>
                        </span>
                        <span class="writor">热度：{$articles.click}</span>
                    </div>
                </div>

                <!--百度分享-->
                <script src='/jiehun/goto/my-65542.js' language='javascript'></script>

                <div class="takeaway">
                    <span class="btn arr-left"></span>
                    <p class="jjxq">{$articles.desc}
</p>
                    <span class="btn arr-right"></span>
                </div>

                  <script src='/jiehun/goto/my-65541.js' language='javascript'></script>

                <div class="substance">
                {$articles.content}
                </div>


                <div class="biaoqian">
                   
                </div>



                <!--相关阅读 -->
                <div class="xgread">
                    <div class="til"><h4>相关阅读</h4></div>
                    <div class="lef"><!--相关阅读主题链接--><script src='/jiehun/goto/my-65540.js' language='javascript'></script></div>
                    <div class="rig">
                        <ul>
                            {volist name="ralateres" id="vo"}
                            <li><a href="{:url('article/index',array('arid'=>$vo[0]))}" target="_blank">{$vo.1}</a></li>
                            {/volist}


                        </ul>
                    </div>
                </div>

                <!--频道推荐-->
                <div class="hotsnew">
                    <div class="til"><h4>频道推荐</h4></div>
                    <ul>
                        {volist name="recres" id="vo"}
                        <li><div class="tu"><a href='{:url('article/index',array('arid'=>$vo['id']))}' target="_blank">
                        <img src="{if condition="$vo['pic'] neq ''"}__IMG__{$vo.pic}{else /}__PUBLIC__/images/error.png{/if}" alt=""/></a></div><p><a href='{:url('article/index',array('arid'=>$vo['id']))}'>{$vo.title}</a></p></li>
                        {/volist}
                    </ul>
                </div>		
            </div>

            {include file="common/right" /}
    
</div>

        {include file="common/foot" /}



    </body>
</html>