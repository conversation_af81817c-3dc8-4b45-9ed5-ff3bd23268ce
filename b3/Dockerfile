FROM phusion/baseimage:jammy-1.0.4

# —— SSH & 源镜像设置 ——
RUN rm -f /etc/service/sshd/down \
 && sed -ri 's/^#?PermitRootLogin\s+.*/PermitRootLogin yes/' /etc/ssh/sshd_config \
 && sed -i "s|http://archive.ubuntu.com|http://mirrors.aliyun.com|g" /etc/apt/sources.list

# —— 安装 Apache2 + PHP(+扩展编译依赖) ——
RUN apt update \
 && apt install -y \
      apache2 \
      php \
      libapache2-mod-php \
      php-mysql \
      php-gd \
      php-dev \
      libmcrypt-dev \
      gcc \
      make \
      autoconf \
      wget \
      ca-certificates \
      openssl \
 && rm -rf /var/lib/apt/lists/*

# —— 安装 MySQL 并预设 root 密码 ——  
ADD ./mysql-passwd /tmp/mysql-passwd
RUN debconf-set-selections /tmp/mysql-passwd \
 && apt update \
 && apt install -y mysql-server \
 && rm -rf /var/lib/apt/lists/* \
 && rm -f /tmp/mysql-passwd

# —— 通过 PECL 安装并启用 mcrypt 扩展 ——  
RUN pecl channel-update pecl.php.net \
 && pecl install mcrypt-1.0.4 \
 && echo "extension=mcrypt.so" \
      > /etc/php/$(php -r 'echo PHP_MAJOR_VERSION.".".PHP_MINOR_VERSION;')/mods-available/mcrypt.ini \
 && phpenmod mcrypt

# —— 数据库初始化脚本 ——  
ADD init_db.sh /tmp/init_db.sh
RUN chmod +x /tmp/init_db.sh
ADD ./init.sql /root/init.sql
RUN /tmp/init_db.sh

# —— 创建部署用户 glzjin ——  
RUN useradd -g www-data glzjin -m \
 && password=$(openssl passwd -1 -salt 'abcdefg' '123456') \
 && sed -i 's/^glzjin:!/glzjin:'"$password"'/g' /etc/shadow

# —— 部署前端静态文件 ——  
RUN rm -f /var/www/html/index.html
COPY --chown=glzjin:www-data ./html/ /var/www/html/
RUN chmod -R 775 /var/www/html/

# —— Apache 配置 ——  
COPY 000-default.conf /etc/apache2/sites-enabled/
COPY apache2.conf     /etc/apache2/
RUN ln -s /etc/apache2/mods-available/rewrite.load /etc/apache2/mods-enabled/

# —— 放置 flag ——  
RUN echo 'flag{glzjin_wants_a_girl_friend}' > /flag.txt

# —— 启动脚本 ——  
ADD ./start.sh /etc/my_init.d/start.sh
RUN chmod +x /etc/my_init.d/start.sh
