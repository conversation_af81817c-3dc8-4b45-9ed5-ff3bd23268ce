<?php

class TP_yyToken implements ArrayAccess
{
    public $string = '';

    public $metadata = array();

    public function __construct($s, $m = array())
    {
        if ($s instanceof TP_yyToken) {
            $this->string = $s->string;
            $this->metadata = $s->metadata;
        } else {
            $this->string = (string) $s;
            if ($m instanceof TP_yyToken) {
                $this->metadata = $m->metadata;
            } elseif (is_array($m)) {
                $this->metadata = $m;
            }
        }
    }

    public function __toString()
    {
        return $this->string;
    }

    public function offsetExists($offset)
    {
        return isset($this->metadata[$offset]);
    }

    public function offsetGet($offset)
    {
        return $this->metadata[$offset];
    }

    public function offsetSet($offset, $value)
    {
        if ($offset === null) {
            if (isset($value[0])) {
                $x = ($value instanceof TP_yyToken) ? $value->metadata : $value;
                $this->metadata = array_merge($this->metadata, $x);

                return;
            }
            $offset = count($this->metadata);
        }
        if ($value === null) {
            return;
        }
        if ($value instanceof TP_yyToken) {
            if ($value->metadata) {
                $this->metadata[$offset] = $value->metadata;
            }
        } elseif ($value) {
            $this->metadata[$offset] = $value;
        }
    }

    public function offsetUnset($offset)
    {
        unset($this->metadata[$offset]);
    }
}

class TP_yyStackEntry
{
    public $stateno;       /* The state-number */
    public $major;         /* The major token value.  This is the code
                     ** number for the token at this stack level */
    public $minor; /* The user-supplied minor token value.  This
                     ** is the value of the token  */
}

;

#line 13 "../smarty/lexer/smarty_internal_templateparser.y"

/**
 * Smarty Internal Plugin Templateparser
 *
 * This is the template parser.
 * It is generated from the smarty_internal_templateparser.y file
 *
 * @package    Smarty
 * @subpackage Compiler
 * <AUTHOR> Tews
 */
class Smarty_Internal_Templateparser
{
    #line 26 "../smarty/lexer/smarty_internal_templateparser.y"

    const Err1 = "Security error: Call to private object member not allowed";

    const Err2 = "Security error: Call to dynamic object member not allowed";

    const Err3 = "PHP in template not allowed. Use SmartyBC to enable it";

    /**
     * result status
     *
     * @var bool
     */
    public $successful = true;

    /**
     * return value
     *
     * @var mixed
     */
    public $retvalue = 0;

    /**
     * counter for prefix code
     *
     * @var int
     */
    public static $prefix_number = 0;

    /**
     * @var
     */
    public $yymajor;

    /**
     * last index of array variable
     *
     * @var mixed
     */
    public $last_index;

    /**
     * last variable name
     *
     * @var string
     */
    public $last_variable;

    /**
     * root parse tree buffer
     *
     * @var Smarty_Internal_ParseTree
     */
    public $root_buffer;

    /**
     * current parse tree object
     *
     * @var Smarty_Internal_ParseTree
     */
    public $current_buffer;

    /**
     * lexer object
     *
     * @var Smarty_Internal_Templatelexer
     */
    private $lex;

    /**
     * internal error flag
     *
     * @var bool
     */
    private $internalError = false;

    /**
     * {strip} status
     *
     * @var bool
     */
    public $strip = false;

    /**
     * compiler object
     *
     * @var Smarty_Internal_TemplateCompilerBase
     */
    public $compiler = null;

    /**
     * smarty object
     *
     * @var Smarty
     */
    public $smarty = null;

    /**
     * template object
     *
     * @var Smarty_Internal_Template
     */
    public $template = null;

    /**
     * block nesting level
     *
     * @var int
     */
    public $block_nesting_level = 0;

    /**
     * security object
     *
     * @var Smarty_Security
     */
    private $security = null;

    /**
     * constructor
     *
     * @param Smarty_Internal_Templatelexer        $lex
     * @param Smarty_Internal_TemplateCompilerBase $compiler
     */
    function __construct(Smarty_Internal_Templatelexer $lex, Smarty_Internal_TemplateCompilerBase $compiler)
    {
        $this->lex = $lex;
        $this->compiler = $compiler;
        $this->template = $this->compiler->template;
        $this->smarty = $this->template->smarty;
        $this->security = isset($this->smarty->security_policy) ? $this->smarty->security_policy : false;
        $this->current_buffer = $this->root_buffer = new Smarty_Internal_ParseTree_Template($this);
    }

    /**
     * insert PHP code in current buffer
     *
     * @param string $code
     */
    public function insertPhpCode($code)
    {
        $this->current_buffer->append_subtree(new Smarty_Internal_ParseTree_Tag($this, $code));
    }

    /**
     *  merge PHP code with prefix code and return parse tree tag object
     *
     * @param string $code
     *
     * @return Smarty_Internal_ParseTree_Tag
     */
    public function mergePrefixCode($code)
    {
        $tmp = '';
        foreach ($this->compiler->prefix_code as $preCode) {
            $tmp = empty($tmp) ? $preCode : $this->compiler->appendCode($tmp, $preCode);
        }
        $this->compiler->prefix_code = array();
        $tmp = empty($tmp) ? $code : $this->compiler->appendCode($tmp, $code);
        return new Smarty_Internal_ParseTree_Tag($this, $this->compiler->processNocacheCode($tmp, true));
    }

    const TP_VERT = 1;

    const TP_COLON = 2;

    const TP_PHP = 3;

    const TP_NOCACHE = 4;

    const TP_TEXT = 5;

    const TP_STRIPON = 6;

    const TP_STRIPOFF = 7;

    const TP_BLOCKSOURCE = 8;

    const TP_LITERALSTART = 9;

    const TP_LITERALEND = 10;

    const TP_LITERAL = 11;

    const TP_RDEL = 12;

    const TP_SIMPLEOUTPUT = 13;

    const TP_LDEL = 14;

    const TP_DOLLARID = 15;

    const TP_EQUAL = 16;

    const TP_SIMPLETAG = 17;

    const TP_ID = 18;

    const TP_PTR = 19;

    const TP_LDELIF = 20;

    const TP_LDELFOR = 21;

    const TP_SEMICOLON = 22;

    const TP_INCDEC = 23;

    const TP_TO = 24;

    const TP_STEP = 25;

    const TP_LDELFOREACH = 26;

    const TP_SPACE = 27;

    const TP_AS = 28;

    const TP_APTR = 29;

    const TP_LDELSETFILTER = 30;

    const TP_SMARTYBLOCKCHILDPARENT = 31;

    const TP_CLOSETAG = 32;

    const TP_LDELSLASH = 33;

    const TP_ATTR = 34;

    const TP_INTEGER = 35;

    const TP_COMMA = 36;

    const TP_OPENP = 37;

    const TP_CLOSEP = 38;

    const TP_MATH = 39;

    const TP_UNIMATH = 40;

    const TP_ISIN = 41;

    const TP_INSTANCEOF = 42;

    const TP_QMARK = 43;

    const TP_NOT = 44;

    const TP_TYPECAST = 45;

    const TP_HEX = 46;

    const TP_DOT = 47;

    const TP_SINGLEQUOTESTRING = 48;

    const TP_DOUBLECOLON = 49;

    const TP_NAMESPACE = 50;

    const TP_AT = 51;

    const TP_HATCH = 52;

    const TP_OPENB = 53;

    const TP_CLOSEB = 54;

    const TP_DOLLAR = 55;

    const TP_LOGOP = 56;

    const TP_TLOGOP = 57;

    const TP_SINGLECOND = 58;

    const TP_QUOTE = 59;

    const TP_BACKTICK = 60;

    const YY_NO_ACTION = 525;

    const YY_ACCEPT_ACTION = 524;

    const YY_ERROR_ACTION = 523;

    const YY_SZ_ACTTAB = 1908;

    static public $yy_action = array(287, 9, 129, 251, 273, 194, 441, 2, 82, 280, 281, 282, 216, 110, 353, 223, 212,
        229, 441, 258, 217, 12, 199, 240, 32, 257, 257, 39, 17, 12, 25, 43, 42, 263, 224, 233, 17, 206, 441, 80, 1, 244,
        311, 311, 172, 172, 52, 287, 9, 128, 441, 273, 65, 178, 2, 82, 268, 14, 184, 298, 110, 262, 13, 319, 229, 297,
        258, 217, 31, 225, 12, 32, 170, 257, 39, 239, 189, 17, 43, 42, 263, 224, 292, 214, 206, 249, 80, 1, 113, 311,
        164, 442, 172, 52, 287, 9, 132, 201, 273, 209, 260, 2, 82, 442, 14, 141, 256, 110, 262, 88, 303, 229, 261, 258,
        217, 260, 225, 12, 32, 168, 36, 39, 241, 12, 17, 43, 42, 263, 224, 292, 17, 206, 189, 80, 1, 7, 311, 180, 257,
        219, 52, 287, 9, 132, 134, 273, 193, 470, 2, 82, 10, 470, 156, 304, 110, 300, 89, 172, 229, 310, 258, 217, 260,
        205, 223, 32, 257, 14, 39, 324, 12, 262, 43, 42, 263, 224, 292, 17, 206, 189, 80, 1, 470, 311, 470, 172, 470,
        52, 287, 9, 131, 201, 273, 209, 257, 2, 82, 83, 307, 232, 201, 110, 399, 454, 230, 229, 237, 258, 217, 454, 225,
        355, 32, 133, 201, 39, 215, 399, 144, 43, 42, 263, 224, 292, 399, 206, 12, 80, 1, 326, 311, 157, 236, 17, 52,
        287, 9, 133, 201, 273, 209, 260, 2, 82, 214, 201, 235, 202, 110, 113, 80, 99, 229, 311, 258, 217, 396, 225, 187,
        19, 14, 323, 39, 18, 262, 28, 43, 42, 263, 224, 292, 396, 206, 12, 80, 1, 143, 311, 396, 134, 17, 52, 287, 9,
        132, 10, 273, 209, 4, 2, 82, 313, 14, 146, 454, 110, 262, 181, 158, 229, 454, 258, 217, 260, 192, 12, 32, 20,
        260, 39, 99, 441, 17, 43, 42, 263, 224, 292, 243, 206, 189, 80, 1, 441, 311, 187, 182, 298, 52, 287, 9, 130,
        201, 273, 209, 14, 2, 82, 93, 262, 104, 24, 110, 399, 99, 169, 229, 154, 258, 217, 220, 225, 113, 5, 124, 260,
        39, 135, 399, 100, 43, 42, 263, 224, 292, 399, 206, 261, 80, 1, 325, 311, 228, 112, 104, 52, 287, 9, 132, 92,
        273, 191, 173, 2, 82, 174, 291, 285, 16, 110, 330, 312, 260, 229, 310, 258, 217, 311, 225, 223, 32, 259, 90, 39,
        261, 6, 264, 43, 42, 263, 224, 292, 181, 206, 175, 80, 1, 116, 311, 171, 201, 21, 52, 287, 9, 132, 37, 273, 195,
        260, 2, 82, 36, 296, 238, 189, 110, 189, 259, 201, 229, 261, 258, 217, 214, 225, 218, 32, 35, 113, 39, 36, 232,
        299, 43, 42, 263, 224, 292, 15, 206, 183, 80, 1, 211, 311, 17, 91, 226, 52, 287, 9, 133, 177, 273, 209, 179, 2,
        82, 318, 470, 99, 18, 110, 470, 454, 121, 229, 288, 258, 217, 289, 225, 316, 19, 145, 189, 39, 187, 189, 121,
        43, 42, 263, 224, 292, 161, 206, 261, 80, 99, 104, 311, 454, 14, 454, 52, 470, 262, 454, 279, 278, 276, 277,
        283, 284, 174, 159, 470, 261, 287, 9, 470, 454, 273, 311, 317, 2, 82, 176, 298, 223, 204, 110, 115, 68, 107,
        229, 117, 258, 217, 100, 3, 201, 272, 329, 138, 29, 210, 271, 293, 454, 325, 454, 359, 470, 260, 454, 254, 317,
        139, 275, 200, 306, 223, 204, 111, 119, 72, 107, 260, 201, 37, 242, 100, 255, 151, 272, 329, 213, 4, 210, 271,
        293, 150, 325, 245, 167, 20, 152, 317, 81, 208, 149, 260, 223, 204, 260, 119, 60, 102, 186, 218, 185, 265, 100,
        269, 22, 272, 329, 286, 270, 210, 271, 293, 317, 325, 248, 147, 148, 223, 204, 178, 119, 72, 107, 153, 232, 260,
        274, 100, 13, 319, 272, 329, 261, 397, 210, 271, 293, 231, 325, 268, 136, 317, 189, 165, 106, 207, 223, 204,
        397, 115, 68, 107, 84, 327, 85, 397, 100, 103, 441, 272, 329, 290, 86, 210, 271, 293, 87, 325, 299, 299, 441,
        317, 299, 155, 299, 299, 223, 204, 305, 119, 50, 102, 299, 108, 299, 299, 100, 299, 299, 272, 329, 299, 299,
        210, 271, 293, 317, 325, 299, 299, 299, 223, 204, 299, 119, 72, 107, 299, 299, 299, 299, 100, 27, 227, 272, 329,
        160, 299, 210, 271, 293, 299, 325, 299, 299, 317, 299, 299, 299, 203, 223, 204, 299, 109, 46, 107, 299, 299,
        299, 299, 100, 299, 299, 272, 329, 299, 308, 210, 271, 293, 299, 325, 299, 311, 287, 8, 309, 299, 273, 299, 317,
        2, 82, 299, 299, 223, 204, 110, 119, 49, 107, 229, 299, 258, 217, 100, 299, 142, 272, 329, 299, 178, 210, 271,
        293, 299, 325, 260, 317, 299, 13, 319, 299, 223, 204, 299, 119, 70, 107, 299, 294, 23, 299, 100, 189, 299, 272,
        329, 299, 299, 210, 271, 293, 317, 325, 299, 299, 299, 223, 204, 299, 119, 77, 107, 299, 299, 299, 299, 100,
        299, 299, 272, 329, 299, 308, 210, 271, 293, 299, 325, 299, 299, 287, 8, 309, 299, 273, 299, 317, 2, 82, 299,
        299, 223, 204, 110, 119, 71, 107, 229, 299, 258, 217, 100, 299, 299, 272, 329, 299, 299, 210, 271, 293, 299,
        325, 299, 317, 299, 299, 299, 299, 223, 204, 299, 119, 60, 107, 299, 295, 23, 299, 100, 299, 299, 272, 329, 299,
        299, 210, 271, 293, 317, 325, 299, 140, 299, 223, 204, 178, 119, 73, 107, 299, 299, 260, 299, 100, 13, 319, 272,
        329, 299, 299, 210, 271, 293, 299, 325, 317, 201, 299, 189, 299, 223, 204, 299, 119, 62, 107, 299, 41, 40, 38,
        100, 299, 299, 272, 329, 299, 299, 210, 271, 293, 299, 325, 317, 163, 321, 322, 328, 223, 204, 299, 119, 63,
        107, 299, 41, 40, 38, 100, 299, 299, 272, 329, 299, 299, 210, 271, 293, 299, 325, 317, 201, 321, 322, 328, 223,
        204, 299, 97, 69, 107, 299, 299, 299, 299, 100, 299, 299, 272, 329, 299, 299, 210, 271, 293, 299, 325, 317, 299,
        299, 299, 299, 223, 204, 299, 119, 75, 107, 222, 41, 40, 38, 100, 299, 299, 272, 329, 299, 299, 210, 271, 293,
        299, 325, 317, 201, 321, 322, 328, 223, 204, 299, 119, 64, 107, 299, 247, 299, 299, 100, 299, 299, 272, 329,
        299, 299, 210, 271, 293, 299, 325, 317, 201, 26, 299, 299, 223, 204, 299, 98, 74, 107, 299, 41, 40, 38, 100,
        299, 299, 272, 329, 299, 299, 210, 271, 293, 299, 325, 317, 201, 321, 322, 328, 223, 198, 299, 105, 59, 107,
        299, 41, 40, 38, 100, 299, 299, 272, 329, 299, 299, 210, 271, 293, 299, 325, 317, 299, 321, 322, 328, 223, 204,
        299, 119, 45, 107, 246, 41, 40, 38, 100, 299, 299, 272, 329, 299, 299, 210, 271, 293, 299, 325, 317, 201, 321,
        322, 328, 223, 94, 299, 79, 48, 101, 299, 252, 299, 299, 100, 299, 299, 272, 329, 299, 299, 210, 271, 293, 299,
        325, 317, 299, 299, 299, 299, 223, 204, 299, 119, 56, 107, 299, 41, 40, 38, 100, 299, 299, 272, 329, 299, 299,
        210, 271, 293, 299, 325, 317, 201, 321, 322, 328, 223, 204, 299, 119, 61, 107, 299, 190, 299, 299, 100, 299,
        299, 272, 329, 299, 299, 210, 271, 293, 299, 325, 317, 299, 299, 299, 299, 223, 204, 299, 96, 58, 107, 299, 41,
        40, 38, 100, 299, 299, 272, 329, 299, 299, 210, 271, 293, 299, 325, 317, 201, 321, 322, 328, 223, 204, 299, 119,
        66, 107, 299, 188, 299, 299, 100, 299, 299, 272, 329, 299, 299, 210, 271, 293, 299, 325, 317, 201, 299, 299,
        299, 223, 204, 299, 119, 47, 107, 299, 41, 40, 38, 100, 299, 299, 272, 329, 299, 299, 210, 271, 293, 299, 325,
        317, 299, 321, 322, 328, 223, 204, 299, 119, 78, 107, 299, 41, 40, 38, 100, 299, 299, 272, 329, 299, 299, 210,
        271, 293, 299, 325, 317, 299, 321, 322, 328, 223, 204, 299, 119, 54, 107, 299, 299, 299, 299, 100, 299, 299,
        272, 329, 299, 299, 210, 271, 293, 299, 325, 317, 299, 299, 299, 299, 223, 204, 299, 119, 53, 107, 299, 299,
        299, 299, 100, 299, 299, 272, 329, 299, 299, 210, 271, 293, 299, 325, 317, 299, 299, 299, 299, 223, 95, 299, 79,
        44, 101, 299, 299, 299, 299, 100, 299, 299, 272, 329, 299, 299, 210, 271, 293, 299, 325, 317, 299, 299, 299,
        299, 223, 197, 299, 119, 57, 107, 299, 299, 299, 299, 100, 299, 299, 272, 329, 299, 299, 210, 271, 293, 299,
        325, 317, 299, 299, 299, 299, 223, 204, 299, 119, 76, 107, 299, 299, 299, 299, 100, 299, 299, 272, 329, 299,
        299, 210, 271, 293, 299, 325, 317, 299, 299, 299, 299, 223, 204, 299, 119, 55, 107, 299, 299, 299, 299, 100,
        299, 299, 272, 329, 299, 299, 210, 271, 293, 299, 325, 317, 299, 299, 299, 299, 223, 204, 299, 119, 67, 107,
        299, 299, 299, 299, 100, 299, 299, 272, 329, 299, 299, 210, 271, 293, 299, 325, 317, 299, 299, 299, 299, 223,
        234, 299, 122, 299, 107, 299, 299, 299, 299, 100, 299, 299, 299, 320, 299, 299, 210, 271, 293, 299, 325, 317,
        409, 409, 299, 299, 223, 234, 299, 127, 299, 107, 299, 299, 299, 299, 100, 299, 299, 299, 250, 299, 299, 210,
        271, 293, 299, 325, 524, 51, 253, 281, 282, 216, 299, 299, 223, 299, 441, 317, 409, 409, 409, 201, 223, 234,
        299, 126, 299, 107, 441, 299, 299, 299, 100, 299, 299, 409, 409, 409, 299, 210, 271, 293, 201, 325, 299, 33,
        299, 12, 299, 299, 299, 299, 299, 301, 17, 299, 299, 299, 299, 41, 40, 38, 299, 299, 317, 299, 299, 299, 12,
        223, 234, 299, 123, 299, 107, 17, 321, 322, 328, 100, 41, 40, 38, 299, 299, 299, 210, 271, 293, 299, 325, 299,
        299, 403, 299, 299, 299, 321, 322, 328, 299, 317, 299, 403, 299, 403, 223, 234, 403, 118, 299, 107, 299, 299,
        299, 403, 100, 403, 299, 403, 299, 299, 299, 210, 271, 293, 317, 325, 232, 299, 299, 223, 234, 299, 125, 299,
        107, 299, 299, 226, 299, 100, 299, 201, 299, 299, 299, 299, 210, 271, 293, 470, 325, 317, 201, 470, 454, 226,
        223, 234, 299, 120, 299, 107, 299, 299, 299, 299, 100, 470, 299, 30, 299, 470, 454, 210, 271, 293, 201, 325, 12,
        41, 40, 38, 454, 299, 454, 17, 470, 299, 454, 314, 41, 40, 38, 299, 315, 299, 321, 322, 328, 201, 454, 34, 454,
        299, 470, 299, 454, 321, 322, 328, 299, 299, 226, 470, 41, 40, 38, 470, 454, 299, 114, 299, 299, 299, 470, 299,
        299, 299, 470, 454, 299, 321, 322, 328, 299, 302, 299, 41, 40, 38, 201, 299, 299, 299, 299, 299, 454, 299, 454,
        299, 470, 365, 454, 299, 321, 322, 328, 454, 221, 454, 299, 470, 299, 454, 166, 299, 12, 299, 178, 299, 299,
        299, 299, 17, 260, 299, 441, 13, 319, 162, 299, 11, 196, 178, 299, 266, 137, 299, 441, 260, 178, 189, 13, 319,
        299, 299, 260, 299, 299, 13, 319, 299, 267, 299, 299, 299, 189, 299, 299, 299, 299, 299, 299, 189, 299, 299,
        299, 299, 299, 299, 299, 299, 311,);

    static public $yy_lookahead = array(13, 14, 15, 15, 17, 18, 37, 20, 21, 64, 65, 66, 67, 26, 12, 70, 47, 30, 49, 32,
        33, 27, 35, 54, 37, 23, 23, 40, 34, 27, 29, 44, 45, 46, 47, 48, 34, 50, 37, 52, 53, 54, 55, 55, 42, 42, 59, 13,
        14, 15, 49, 17, 18, 76, 20, 21, 93, 14, 95, 96, 26, 18, 85, 86, 30, 31, 32, 33, 16, 35, 27, 37, 29, 23, 40, 23,
        99, 34, 44, 45, 46, 47, 48, 75, 50, 77, 52, 53, 80, 55, 72, 37, 42, 59, 13, 14, 15, 1, 17, 18, 82, 20, 21, 49,
        14, 72, 18, 26, 18, 76, 60, 30, 94, 32, 33, 82, 35, 27, 37, 29, 36, 40, 38, 27, 34, 44, 45, 46, 47, 48, 34, 50,
        99, 52, 53, 36, 55, 81, 23, 51, 59, 13, 14, 15, 47, 17, 18, 14, 20, 21, 53, 18, 72, 54, 26, 12, 76, 42, 30, 65,
        32, 33, 82, 35, 70, 37, 23, 14, 40, 54, 27, 18, 44, 45, 46, 47, 48, 34, 50, 99, 52, 53, 14, 55, 51, 42, 18, 59,
        13, 14, 15, 1, 17, 18, 23, 20, 21, 103, 104, 47, 1, 26, 12, 47, 51, 30, 54, 32, 33, 53, 35, 12, 37, 15, 1, 40,
        18, 27, 28, 44, 45, 46, 47, 48, 34, 50, 27, 52, 53, 18, 55, 72, 19, 34, 59, 13, 14, 15, 1, 17, 18, 82, 20, 21,
        75, 1, 77, 78, 26, 80, 52, 19, 30, 55, 32, 33, 12, 35, 99, 37, 14, 50, 40, 16, 18, 14, 44, 45, 46, 47, 48, 27,
        50, 27, 52, 53, 92, 55, 34, 47, 34, 59, 13, 14, 15, 53, 17, 18, 37, 20, 21, 54, 14, 72, 47, 26, 18, 76, 72, 30,
        53, 32, 33, 82, 35, 27, 37, 16, 82, 40, 19, 37, 34, 44, 45, 46, 47, 48, 54, 50, 99, 52, 53, 49, 55, 99, 95, 96,
        59, 13, 14, 15, 1, 17, 18, 14, 20, 21, 81, 18, 49, 16, 26, 12, 19, 72, 30, 75, 32, 33, 71, 35, 80, 37, 75, 82,
        40, 15, 27, 80, 44, 45, 46, 47, 48, 34, 50, 94, 52, 53, 91, 55, 51, 80, 49, 59, 13, 14, 15, 37, 17, 18, 72, 20,
        21, 9, 10, 11, 22, 26, 97, 96, 82, 30, 65, 32, 33, 55, 35, 70, 37, 100, 36, 40, 94, 37, 35, 44, 45, 46, 47, 48,
        76, 50, 76, 52, 53, 49, 55, 72, 1, 16, 59, 13, 14, 15, 2, 17, 18, 82, 20, 21, 36, 104, 38, 99, 26, 99, 100, 1,
        30, 94, 32, 33, 75, 35, 77, 37, 29, 80, 40, 36, 47, 38, 44, 45, 46, 47, 48, 27, 50, 15, 52, 53, 18, 55, 34, 92,
        2, 59, 13, 14, 15, 76, 17, 18, 76, 20, 21, 90, 14, 19, 16, 26, 18, 19, 97, 30, 66, 32, 33, 69, 35, 90, 37, 15,
        99, 40, 99, 99, 97, 44, 45, 46, 47, 48, 92, 50, 94, 52, 19, 49, 55, 47, 14, 49, 59, 51, 18, 53, 3, 4, 5, 6, 7,
        8, 9, 92, 14, 94, 13, 14, 18, 19, 17, 55, 65, 20, 21, 95, 96, 70, 71, 26, 73, 74, 75, 30, 18, 32, 33, 80, 37, 1,
        83, 84, 72, 24, 87, 88, 89, 47, 91, 49, 12, 51, 82, 53, 18, 65, 72, 5, 101, 102, 70, 71, 18, 73, 74, 75, 82, 1,
        2, 38, 80, 18, 52, 83, 84, 18, 37, 87, 88, 89, 72, 91, 54, 72, 16, 52, 65, 18, 98, 92, 82, 70, 71, 82, 73, 74,
        75, 18, 77, 81, 18, 80, 18, 43, 83, 84, 12, 35, 87, 88, 89, 65, 91, 18, 72, 92, 70, 71, 76, 73, 74, 75, 92, 47,
        82, 82, 80, 85, 86, 83, 84, 94, 12, 87, 88, 89, 16, 91, 93, 80, 65, 99, 92, 79, 98, 70, 71, 27, 73, 74, 75, 80,
        87, 80, 34, 80, 68, 37, 83, 84, 10, 80, 87, 88, 89, 80, 91, 105, 105, 49, 65, 105, 92, 105, 105, 70, 71, 102,
        73, 74, 75, 105, 77, 105, 105, 80, 105, 105, 83, 84, 105, 105, 87, 88, 89, 65, 91, 105, 105, 105, 70, 71, 105,
        73, 74, 75, 105, 105, 105, 105, 80, 14, 15, 83, 84, 18, 105, 87, 88, 89, 105, 91, 105, 105, 65, 105, 105, 105,
        98, 70, 71, 105, 73, 74, 75, 105, 105, 105, 105, 80, 105, 105, 83, 84, 105, 5, 87, 88, 89, 105, 91, 105, 55, 13,
        14, 15, 105, 17, 105, 65, 20, 21, 105, 105, 70, 71, 26, 73, 74, 75, 30, 105, 32, 33, 80, 105, 72, 83, 84, 105,
        76, 87, 88, 89, 105, 91, 82, 65, 105, 85, 86, 105, 70, 71, 105, 73, 74, 75, 105, 59, 60, 105, 80, 99, 105, 83,
        84, 105, 105, 87, 88, 89, 65, 91, 105, 105, 105, 70, 71, 105, 73, 74, 75, 105, 105, 105, 105, 80, 105, 105, 83,
        84, 105, 5, 87, 88, 89, 105, 91, 105, 105, 13, 14, 15, 105, 17, 105, 65, 20, 21, 105, 105, 70, 71, 26, 73, 74,
        75, 30, 105, 32, 33, 80, 105, 105, 83, 84, 105, 105, 87, 88, 89, 105, 91, 105, 65, 105, 105, 105, 105, 70, 71,
        105, 73, 74, 75, 105, 59, 60, 105, 80, 105, 105, 83, 84, 105, 105, 87, 88, 89, 65, 91, 105, 72, 105, 70, 71, 76,
        73, 74, 75, 105, 105, 82, 105, 80, 85, 86, 83, 84, 105, 105, 87, 88, 89, 105, 91, 65, 1, 105, 99, 105, 70, 71,
        105, 73, 74, 75, 105, 39, 40, 41, 80, 105, 105, 83, 84, 105, 105, 87, 88, 89, 105, 91, 65, 28, 56, 57, 58, 70,
        71, 105, 73, 74, 75, 105, 39, 40, 41, 80, 105, 105, 83, 84, 105, 105, 87, 88, 89, 105, 91, 65, 1, 56, 57, 58,
        70, 71, 105, 73, 74, 75, 105, 105, 105, 105, 80, 105, 105, 83, 84, 105, 105, 87, 88, 89, 105, 91, 65, 105, 105,
        105, 105, 70, 71, 105, 73, 74, 75, 38, 39, 40, 41, 80, 105, 105, 83, 84, 105, 105, 87, 88, 89, 105, 91, 65, 1,
        56, 57, 58, 70, 71, 105, 73, 74, 75, 105, 12, 105, 105, 80, 105, 105, 83, 84, 105, 105, 87, 88, 89, 105, 91, 65,
        1, 2, 105, 105, 70, 71, 105, 73, 74, 75, 105, 39, 40, 41, 80, 105, 105, 83, 84, 105, 105, 87, 88, 89, 105, 91,
        65, 1, 56, 57, 58, 70, 71, 105, 73, 74, 75, 105, 39, 40, 41, 80, 105, 105, 83, 84, 105, 105, 87, 88, 89, 105,
        91, 65, 105, 56, 57, 58, 70, 71, 105, 73, 74, 75, 38, 39, 40, 41, 80, 105, 105, 83, 84, 105, 105, 87, 88, 89,
        105, 91, 65, 1, 56, 57, 58, 70, 71, 105, 73, 74, 75, 105, 12, 105, 105, 80, 105, 105, 83, 84, 105, 105, 87, 88,
        89, 105, 91, 65, 105, 105, 105, 105, 70, 71, 105, 73, 74, 75, 105, 39, 40, 41, 80, 105, 105, 83, 84, 105, 105,
        87, 88, 89, 105, 91, 65, 1, 56, 57, 58, 70, 71, 105, 73, 74, 75, 105, 12, 105, 105, 80, 105, 105, 83, 84, 105,
        105, 87, 88, 89, 105, 91, 65, 105, 105, 105, 105, 70, 71, 105, 73, 74, 75, 105, 39, 40, 41, 80, 105, 105, 83,
        84, 105, 105, 87, 88, 89, 105, 91, 65, 1, 56, 57, 58, 70, 71, 105, 73, 74, 75, 105, 12, 105, 105, 80, 105, 105,
        83, 84, 105, 105, 87, 88, 89, 105, 91, 65, 1, 105, 105, 105, 70, 71, 105, 73, 74, 75, 105, 39, 40, 41, 80, 105,
        105, 83, 84, 105, 105, 87, 88, 89, 105, 91, 65, 105, 56, 57, 58, 70, 71, 105, 73, 74, 75, 105, 39, 40, 41, 80,
        105, 105, 83, 84, 105, 105, 87, 88, 89, 105, 91, 65, 105, 56, 57, 58, 70, 71, 105, 73, 74, 75, 105, 105, 105,
        105, 80, 105, 105, 83, 84, 105, 105, 87, 88, 89, 105, 91, 65, 105, 105, 105, 105, 70, 71, 105, 73, 74, 75, 105,
        105, 105, 105, 80, 105, 105, 83, 84, 105, 105, 87, 88, 89, 105, 91, 65, 105, 105, 105, 105, 70, 71, 105, 73, 74,
        75, 105, 105, 105, 105, 80, 105, 105, 83, 84, 105, 105, 87, 88, 89, 105, 91, 65, 105, 105, 105, 105, 70, 71,
        105, 73, 74, 75, 105, 105, 105, 105, 80, 105, 105, 83, 84, 105, 105, 87, 88, 89, 105, 91, 65, 105, 105, 105,
        105, 70, 71, 105, 73, 74, 75, 105, 105, 105, 105, 80, 105, 105, 83, 84, 105, 105, 87, 88, 89, 105, 91, 65, 105,
        105, 105, 105, 70, 71, 105, 73, 74, 75, 105, 105, 105, 105, 80, 105, 105, 83, 84, 105, 105, 87, 88, 89, 105, 91,
        65, 105, 105, 105, 105, 70, 71, 105, 73, 74, 75, 105, 105, 105, 105, 80, 105, 105, 83, 84, 105, 105, 87, 88, 89,
        105, 91, 65, 105, 105, 105, 105, 70, 71, 105, 73, 105, 75, 105, 105, 105, 105, 80, 105, 105, 105, 84, 105, 105,
        87, 88, 89, 105, 91, 65, 1, 2, 105, 105, 70, 71, 105, 73, 105, 75, 105, 105, 105, 105, 80, 105, 105, 105, 84,
        105, 105, 87, 88, 89, 105, 91, 62, 63, 64, 65, 66, 67, 105, 105, 70, 105, 37, 65, 39, 40, 41, 1, 70, 71, 105,
        73, 105, 75, 49, 105, 105, 105, 80, 105, 105, 56, 57, 58, 105, 87, 88, 89, 1, 91, 105, 25, 105, 27, 105, 105,
        105, 105, 105, 12, 34, 105, 105, 105, 105, 39, 40, 41, 105, 105, 65, 105, 105, 105, 27, 70, 71, 105, 73, 105,
        75, 34, 56, 57, 58, 80, 39, 40, 41, 105, 105, 105, 87, 88, 89, 105, 91, 105, 105, 12, 105, 105, 105, 56, 57, 58,
        105, 65, 105, 22, 105, 24, 70, 71, 27, 73, 105, 75, 105, 105, 105, 34, 80, 36, 105, 38, 105, 105, 105, 87, 88,
        89, 65, 91, 47, 105, 105, 70, 71, 105, 73, 105, 75, 105, 105, 2, 105, 80, 105, 1, 105, 105, 105, 105, 87, 88,
        89, 14, 91, 65, 1, 18, 19, 2, 70, 71, 105, 73, 105, 75, 105, 105, 105, 105, 80, 14, 105, 16, 105, 18, 19, 87,
        88, 89, 1, 91, 27, 39, 40, 41, 47, 105, 49, 34, 51, 105, 53, 54, 39, 40, 41, 105, 54, 105, 56, 57, 58, 1, 47, 2,
        49, 105, 51, 105, 53, 56, 57, 58, 105, 105, 2, 14, 39, 40, 41, 18, 19, 105, 22, 105, 105, 105, 14, 105, 105,
        105, 18, 19, 105, 56, 57, 58, 105, 60, 105, 39, 40, 41, 1, 105, 105, 105, 105, 105, 47, 105, 49, 105, 51, 12,
        53, 105, 56, 57, 58, 47, 19, 49, 105, 51, 105, 53, 72, 105, 27, 105, 76, 105, 105, 105, 105, 34, 82, 105, 37,
        85, 86, 72, 105, 14, 15, 76, 105, 18, 72, 105, 49, 82, 76, 99, 85, 86, 105, 105, 82, 105, 105, 85, 86, 105, 35,
        105, 105, 105, 99, 105, 105, 105, 105, 105, 105, 99, 105, 105, 105, 105, 105, 105, 105, 105, 55,);

    const YY_SHIFT_USE_DFLT = - 32;

    const YY_SHIFT_MAX = 236;

    static public $yy_shift_ofst = array(517, 410, 316, 81, 81, 316, 81, 410, 34, 34, - 13, 81, 128, 81, 81, 128, 81,
        81, 269, 81, 81, 81, 175, 81, 81, 81, 81, 81, 81, 81, 81, 81, 81, 81, 363, 81, 81, 222, 222, 457, 457, 457, 457,
        457, 1624, 1603, 1736, 1736, 1736, 1736, 1736, 517, 754, 1211, 1265, 1076, 1157, 1760, 941, 1725, 995, 1103,
        1049, 1783, 1292, 1824, 1292, 1292, 1292, 1292, 1292, 1292, 1292, 1292, 1292, 1292, 1292, 914, 914, 199, 198,
        96, 342, 842, 90, 43, 278, 246, 96, 96, 342, 232, 342, 580, 2, 143, 190, 244, 331, 711, 321, 325, 291, 376, 446,
        237, - 6, 462, - 6, 552, 432, 213, 500, 500, 480, 419, 446, 438, 438, 438, 438, 491, 438, 438, 491, 438, 438,
        - 32, 1738, 1720, 466, 1784, 1795, 514, 1852, 247, 153, - 6, - 6, - 6, - 6, - 6, - 6, 97, - 12, 168, - 6, - 6,
        97, 97, - 6, 156, 156, 97, 52, 97, - 6, - 6, - 6, 97, 251, 97, - 6, - 12, - 6, 97, - 6, - 6, - 12, - 6, - 12,
        - 6, 211, - 6, 664, 438, 491, 438, 438, 438, 424, 438, 491, 515, 491, 424, - 32, - 32, - 32, - 32, - 32, 1562,
        1664, 634, - 31, 1, 133, 50, 115, 152, 99, 88, 366, 84, 3, 405, 54, 415, 396, 274, 368, 553, 571, 542, 582, 534,
        566, 558, 545, 567, 547, 583, 574, 608, 586, 590, 598, 515, 550, 593, 596, 609, 371, 264, 171, 533, 530,);

    const YY_REDUCE_USE_DFLT = - 56;

    const YY_REDUCE_MAX = 190;

    static public $yy_reduce_ofst = array(1527, 471, 619, 560, 644, 535, 504, 589, 1335, 1092, 1038, 1119, 1011, 984,
        876, 1173, 903, 930, 957, 1146, 1200, 1443, 1416, 1362, 1227, 1389, 1254, 1281, 1308, 1065, 673, 849, 824, 708,
        761, 736, 796, 1497, 1470, 1619, 1535, 1671, 1644, 1582, 1792, 1777, 1799, 845, 1792, 718, 556, - 55, 94, - 23,
        - 23, - 23, - 23, - 23, - 23, - 23, - 23, - 23, - 23, - 23, - 23, 33, - 23, - 23, - 23, - 23, - 23, - 23, - 23,
        - 23, - 23, - 23, - 23, - 23, - 23, 221, 279, 80, 169, 329, 347, 310, 18, 273, 159, 226, 8, - 37, 369, 338, 525,
        525, 336, 336, 336, 293, 414, 231, 231, 422, 389, 336, 522, 231, 498, 336, 484, 400, 435, 414, 272, 336, 403,
        397, 336, 336, 336, 444, 336, 336, 231, 336, 336, 336, 184, 184, 184, 184, 184, 184, 573, 184, 551, 557, 557,
        557, 557, 557, 557, 559, 585, 184, 557, 557, 559, 559, 557, 544, 564, 559, 578, 559, 557, 557, 557, 559, 594,
        559, 557, 587, 557, 559, 557, 557, 595, 557, 599, 557, 579, 557, 602, 399, 295, 399, 399, 399, 301, 399, 295,
        375, 295, 301, 257, 56, 537, 532, 511,);

    static public $yyExpectedTokens = array(array(3, 4, 5, 6, 7, 8, 9, 13, 14, 17, 20, 21, 26, 30, 32, 33,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 31, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 31, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 54, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 53, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 55, 59,),
        array(13, 14, 15, 17, 18, 20, 21, 26, 30, 32, 33, 35, 37, 40, 44, 45, 46, 47, 48, 50, 52, 55, 59,),
        array(1, 12, 27, 34, 39, 40, 41, 56, 57, 58,), array(1, 25, 27, 34, 39, 40, 41, 56, 57, 58,),
        array(1, 27, 34, 39, 40, 41, 56, 57, 58,), array(1, 27, 34, 39, 40, 41, 56, 57, 58,),
        array(1, 27, 34, 39, 40, 41, 56, 57, 58,), array(1, 27, 34, 39, 40, 41, 56, 57, 58,),
        array(1, 27, 34, 39, 40, 41, 56, 57, 58,), array(3, 4, 5, 6, 7, 8, 9, 13, 14, 17, 20, 21, 26, 30, 32, 33,),
        array(5, 13, 14, 15, 17, 20, 21, 26, 30, 32, 33, 59, 60,), array(1, 12, 39, 40, 41, 56, 57, 58,),
        array(1, 12, 39, 40, 41, 56, 57, 58,), array(1, 2, 39, 40, 41, 56, 57, 58,),
        array(1, 12, 39, 40, 41, 56, 57, 58,), array(1, 39, 40, 41, 56, 57, 58, 60,),
        array(1, 28, 39, 40, 41, 56, 57, 58,), array(1, 39, 40, 41, 54, 56, 57, 58,),
        array(1, 38, 39, 40, 41, 56, 57, 58,), array(1, 38, 39, 40, 41, 56, 57, 58,),
        array(1, 12, 39, 40, 41, 56, 57, 58,), array(1, 22, 39, 40, 41, 56, 57, 58,), array(1, 39, 40, 41, 56, 57, 58,),
        array(1, 12, 19, 27, 34, 37, 49,), array(1, 39, 40, 41, 56, 57, 58,), array(1, 39, 40, 41, 56, 57, 58,),
        array(1, 39, 40, 41, 56, 57, 58,), array(1, 39, 40, 41, 56, 57, 58,), array(1, 39, 40, 41, 56, 57, 58,),
        array(1, 39, 40, 41, 56, 57, 58,), array(1, 39, 40, 41, 56, 57, 58,), array(1, 39, 40, 41, 56, 57, 58,),
        array(1, 39, 40, 41, 56, 57, 58,), array(1, 39, 40, 41, 56, 57, 58,), array(1, 39, 40, 41, 56, 57, 58,),
        array(39, 40, 41, 56, 57, 58,), array(39, 40, 41, 56, 57, 58,), array(1, 12, 27, 34,), array(15, 18, 52, 55,),
        array(1, 27, 34,), array(15, 37, 55,), array(5, 13, 14, 15, 17, 20, 21, 26, 30, 32, 33, 59, 60,),
        array(14, 18, 27, 29, 34,), array(14, 18, 27, 29, 34,), array(14, 18, 27, 34,), array(14, 18, 27, 34,),
        array(1, 27, 34,), array(1, 27, 34,), array(15, 37, 55,), array(19, 47, 53,), array(15, 37, 55,), array(1, 2,),
        array(12, 23, 27, 34, 42,), array(12, 23, 27, 34, 42,), array(1, 12, 27, 28, 34,), array(1, 12, 27, 34,),
        array(1, 12, 27, 34,), array(14, 15, 18, 55,), array(14, 18, 51,), array(16, 19, 49,), array(16, 19, 49,),
        array(9, 10, 11,), array(15, 18,), array(1, 54,), array(27, 34,), array(19, 49,), array(27, 34,), array(1, 12,),
        array(27, 34,), array(1, 19,), array(14, 18,), array(14, 18,), array(15, 55,), array(1, 29,), array(15, 18,),
        array(1,), array(1,), array(1,), array(1,), array(19,), array(1,), array(1,), array(19,), array(1,), array(1,),
        array(), array(2, 14, 16, 18, 19, 47, 49, 51, 53,), array(2, 14, 18, 19, 47, 49, 51, 53, 54,),
        array(2, 14, 16, 18, 19, 47, 49, 51, 53,), array(2, 14, 18, 19, 47, 49, 51, 53,),
        array(2, 14, 18, 19, 47, 49, 51, 53,), array(14, 18, 19, 47, 49, 51, 53,), array(14, 15, 18, 35, 55,),
        array(16, 47, 53,), array(14, 18, 51,), array(27, 34,), array(27, 34,), array(27, 34,), array(27, 34,),
        array(27, 34,), array(27, 34,), array(47, 53,), array(15, 55,), array(14, 18,), array(27, 34,), array(27, 34,),
        array(47, 53,), array(47, 53,), array(27, 34,), array(47, 53,), array(47, 53,), array(47, 53,), array(16, 23,),
        array(47, 53,), array(27, 34,), array(27, 34,), array(27, 34,), array(47, 53,), array(14, 37,), array(47, 53,),
        array(27, 34,), array(15, 55,), array(27, 34,), array(47, 53,), array(27, 34,), array(27, 34,), array(15, 55,),
        array(27, 34,), array(15, 55,), array(27, 34,), array(18, 50,), array(27, 34,), array(10,), array(1,),
        array(19,), array(1,), array(1,), array(1,), array(2,), array(1,), array(19,), array(37,), array(19,),
        array(2,), array(), array(), array(), array(), array(), array(1, 2, 37, 39, 40, 41, 49, 56, 57, 58,),
        array(12, 22, 24, 27, 34, 36, 38, 47,), array(12, 16, 27, 34, 37, 49,), array(37, 47, 49, 54,),
        array(29, 37, 49,), array(14, 18, 51,), array(23, 42, 60,), array(23, 42, 54,), array(47, 54,), array(36, 54,),
        array(18, 51,), array(22, 36,), array(36, 38,), array(23, 42,), array(16, 47,), array(37, 49,), array(36, 38,),
        array(36, 38,), array(37, 49,), array(37, 49,), array(37,), array(18,), array(54,), array(16,), array(52,),
        array(5,), array(18,), array(38,), array(18,), array(52,), array(18,), array(43,), array(12,), array(35,),
        array(47,), array(18,), array(37,), array(18,), array(18,), array(18,), array(18,), array(35,), array(54,),
        array(23,), array(24,), array(18,), array(), array(), array(), array(), array(), array(), array(), array(),
        array(), array(), array(), array(), array(), array(), array(), array(), array(), array(), array(), array(),
        array(), array(), array(), array(), array(), array(), array(), array(), array(), array(), array(), array(),
        array(), array(), array(), array(), array(), array(), array(), array(), array(), array(), array(), array(),
        array(), array(), array(), array(), array(), array(), array(), array(), array(), array(), array(), array(),
        array(), array(), array(), array(), array(), array(), array(), array(), array(), array(), array(), array(),
        array(), array(), array(), array(), array(), array(), array(), array(), array(), array(), array(), array(),
        array(), array(), array(), array(), array(), array(), array(), array(), array(), array(), array(), array(),
        array(), array(),);

    static public $yy_default = array(334, 508, 523, 488, 488, 523, 488, 523, 523, 523, 523, 523, 523, 523, 523, 523,
        523, 523, 523, 523, 523, 523, 523, 523, 523, 523, 523, 523, 523, 523, 523, 523, 523, 523, 523, 523, 523, 523,
        523, 523, 523, 523, 523, 523, 523, 393, 360, 393, 357, 393, 369, 331, 523, 523, 523, 523, 523, 523, 398, 523,
        523, 523, 523, 523, 414, 431, 405, 400, 511, 395, 509, 486, 487, 374, 398, 404, 510, 419, 420, 407, 523, 393,
        523, 523, 393, 393, 393, 393, 393, 393, 523, 500, 523, 383, 421, 421, 407, 407, 407, 523, 454, 444, 444, 523,
        523, 407, 393, 444, 371, 407, 393, 387, 454, 454, 523, 407, 523, 389, 422, 407, 410, 497, 417, 423, 444, 424,
        411, 495, 443, 443, 443, 443, 443, 443, 523, 470, 456, 361, 378, 372, 362, 364, 377, 451, 523, 454, 356, 370,
        480, 481, 373, 447, 449, 448, 523, 478, 367, 366, 368, 479, 454, 452, 358, 523, 380, 450, 376, 354, 523, 382,
        523, 379, 523, 381, 348, 384, 498, 390, 413, 388, 489, 438, 475, 454, 501, 490, 494, 494, 454, 494, 454, 431,
        427, 431, 431, 431, 455, 421, 421, 427, 523, 523, 523, 523, 421, 427, 439, 523, 523, 431, 523, 499, 523, 523,
        523, 523, 339, 523, 523, 523, 523, 523, 433, 523, 523, 427, 523, 470, 523, 523, 523, 523, 429, 434, 421, 401,
        523, 464, 483, 375, 461, 484, 406, 463, 469, 462, 433, 474, 394, 402, 496, 470, 460, 332, 445, 491, 492, 425,
        386, 493, 392, 472, 473, 426, 428, 457, 458, 459, 453, 409, 430, 432, 408, 363, 391, 341, 340, 342, 338, 337,
        333, 335, 336, 343, 344, 350, 351, 352, 349, 347, 345, 346, 434, 435, 512, 513, 514, 385, 476, 485, 519, 520,
        517, 516, 505, 507, 506, 515, 522, 518, 521, 471, 477, 467, 465, 468, 440, 437, 436, 415, 416, 502, 503, 442,
        466, 446, 441, 418, 504, 412, 482,);

    const YYNOCODE = 106;

    const YYSTACKDEPTH = 500;

    const YYNSTATE = 331;

    const YYNRULE = 192;

    const YYERRORSYMBOL = 61;

    const YYERRSYMDT = 'yy0';

    const YYFALLBACK = 0;

    public static $yyFallback = array();

    public function Trace($TraceFILE, $zTracePrompt)
    {
        if (!$TraceFILE) {
            $zTracePrompt = 0;
        } elseif (!$zTracePrompt) {
            $TraceFILE = 0;
        }
        $this->yyTraceFILE = $TraceFILE;
        $this->yyTracePrompt = $zTracePrompt;
    }

    public function PrintTrace()
    {
        $this->yyTraceFILE = fopen('php://output', 'w');
        $this->yyTracePrompt = '<br>';
    }

    public $yyTraceFILE;

    public $yyTracePrompt;

    public $yyidx;                    /* Index of top element in stack */
    public $yyerrcnt;                 /* Shifts left before out of the error */
    public $yystack = array();  /* The parser's stack */

    public $yyTokenName = array('$', 'VERT', 'COLON', 'PHP', 'NOCACHE', 'TEXT', 'STRIPON', 'STRIPOFF', 'BLOCKSOURCE',
        'LITERALSTART', 'LITERALEND', 'LITERAL', 'RDEL', 'SIMPLEOUTPUT', 'LDEL', 'DOLLARID', 'EQUAL', 'SIMPLETAG', 'ID',
        'PTR', 'LDELIF', 'LDELFOR', 'SEMICOLON', 'INCDEC', 'TO', 'STEP', 'LDELFOREACH', 'SPACE', 'AS', 'APTR',
        'LDELSETFILTER', 'SMARTYBLOCKCHILDPARENT', 'CLOSETAG', 'LDELSLASH', 'ATTR', 'INTEGER', 'COMMA', 'OPENP',
        'CLOSEP', 'MATH', 'UNIMATH', 'ISIN', 'INSTANCEOF', 'QMARK', 'NOT', 'TYPECAST', 'HEX', 'DOT',
        'SINGLEQUOTESTRING', 'DOUBLECOLON', 'NAMESPACE', 'AT', 'HATCH', 'OPENB', 'CLOSEB', 'DOLLAR', 'LOGOP', 'TLOGOP',
        'SINGLECOND', 'QUOTE', 'BACKTICK', 'error', 'start', 'template', 'template_element', 'smartytag', 'literal',
        'text_content', 'literal_elements', 'literal_element', 'tag', 'variable', 'attributes', 'value', 'expr',
        'varindexed', 'modifierlist', 'statement', 'statements', 'foraction', 'varvar', 'modparameters', 'attribute',
        'ternary', 'array', 'lop', 'scond', 'ns1', 'function', 'doublequoted_with_quotes', 'static_class_access',
        'object', 'arrayindex', 'indexdef', 'varvarele', 'objectchain', 'objectelement', 'method', 'params', 'modifier',
        'modparameter', 'arrayelements', 'arrayelement', 'doublequoted', 'doublequotedcontent',);

    public static $yyRuleName = array('start ::= template', 'template ::= template_element',
        'template ::= template template_element', 'template ::=', 'template_element ::= smartytag',
        'template_element ::= literal', 'template_element ::= PHP', 'template_element ::= NOCACHE',
        'template_element ::= text_content', 'text_content ::= TEXT', 'text_content ::= text_content TEXT',
        'template_element ::= STRIPON', 'template_element ::= STRIPOFF', 'template_element ::= BLOCKSOURCE',
        'literal ::= LITERALSTART LITERALEND', 'literal ::= LITERALSTART literal_elements LITERALEND',
        'literal_elements ::= literal_elements literal_element', 'literal_elements ::=', 'literal_element ::= literal',
        'literal_element ::= LITERAL', 'smartytag ::= tag RDEL', 'smartytag ::= SIMPLEOUTPUT', 'tag ::= LDEL variable',
        'tag ::= LDEL variable attributes', 'tag ::= LDEL value', 'tag ::= LDEL value attributes', 'tag ::= LDEL expr',
        'tag ::= LDEL expr attributes', 'tag ::= LDEL DOLLARID EQUAL value', 'tag ::= LDEL DOLLARID EQUAL expr',
        'tag ::= LDEL DOLLARID EQUAL expr attributes', 'tag ::= LDEL varindexed EQUAL expr attributes',
        'smartytag ::= SIMPLETAG', 'tag ::= LDEL ID attributes', 'tag ::= LDEL ID',
        'tag ::= LDEL ID modifierlist attributes', 'tag ::= LDEL ID PTR ID attributes',
        'tag ::= LDEL ID PTR ID modifierlist attributes', 'tag ::= LDELIF expr', 'tag ::= LDELIF expr attributes',
        'tag ::= LDELIF statement', 'tag ::= LDELIF statement attributes',
        'tag ::= LDELFOR statements SEMICOLON expr SEMICOLON varindexed foraction attributes',
        'foraction ::= EQUAL expr', 'foraction ::= INCDEC', 'tag ::= LDELFOR statement TO expr attributes',
        'tag ::= LDELFOR statement TO expr STEP expr attributes', 'tag ::= LDELFOREACH attributes',
        'tag ::= LDELFOREACH SPACE value AS varvar attributes',
        'tag ::= LDELFOREACH SPACE value AS varvar APTR varvar attributes',
        'tag ::= LDELFOREACH SPACE expr AS varvar attributes',
        'tag ::= LDELFOREACH SPACE expr AS varvar APTR varvar attributes', 'tag ::= LDELSETFILTER ID modparameters',
        'tag ::= LDELSETFILTER ID modparameters modifierlist', 'tag ::= LDEL SMARTYBLOCKCHILDPARENT',
        'smartytag ::= CLOSETAG', 'tag ::= LDELSLASH ID', 'tag ::= LDELSLASH ID modifierlist',
        'tag ::= LDELSLASH ID PTR ID', 'tag ::= LDELSLASH ID PTR ID modifierlist',
        'attributes ::= attributes attribute', 'attributes ::= attribute', 'attributes ::=',
        'attribute ::= SPACE ID EQUAL ID', 'attribute ::= ATTR expr', 'attribute ::= ATTR value',
        'attribute ::= SPACE ID', 'attribute ::= SPACE expr', 'attribute ::= SPACE value',
        'attribute ::= SPACE INTEGER EQUAL expr', 'statements ::= statement',
        'statements ::= statements COMMA statement', 'statement ::= DOLLARID EQUAL INTEGER',
        'statement ::= DOLLARID EQUAL expr', 'statement ::= varindexed EQUAL expr',
        'statement ::= OPENP statement CLOSEP', 'expr ::= value', 'expr ::= ternary', 'expr ::= DOLLARID COLON ID',
        'expr ::= expr MATH value', 'expr ::= expr UNIMATH value', 'expr ::= array', 'expr ::= expr modifierlist',
        'expr ::= expr lop expr', 'expr ::= expr scond', 'expr ::= expr ISIN array', 'expr ::= expr ISIN value',
        'expr ::= variable INSTANCEOF ns1', 'ternary ::= OPENP expr CLOSEP QMARK DOLLARID COLON expr',
        'ternary ::= OPENP expr CLOSEP QMARK expr COLON expr', 'value ::= variable', 'value ::= UNIMATH value',
        'value ::= NOT value', 'value ::= TYPECAST value', 'value ::= variable INCDEC', 'value ::= HEX',
        'value ::= INTEGER', 'value ::= INTEGER DOT INTEGER', 'value ::= INTEGER DOT', 'value ::= DOT INTEGER',
        'value ::= ID', 'value ::= function', 'value ::= OPENP expr CLOSEP', 'value ::= SINGLEQUOTESTRING',
        'value ::= doublequoted_with_quotes', 'value ::= varindexed DOUBLECOLON static_class_access',
        'value ::= smartytag', 'value ::= value modifierlist', 'value ::= NAMESPACE',
        'value ::= ns1 DOUBLECOLON static_class_access', 'ns1 ::= ID', 'ns1 ::= NAMESPACE', 'variable ::= DOLLARID',
        'variable ::= varindexed', 'variable ::= varvar AT ID', 'variable ::= object', 'variable ::= HATCH ID HATCH',
        'variable ::= HATCH ID HATCH arrayindex', 'variable ::= HATCH variable HATCH',
        'variable ::= HATCH variable HATCH arrayindex', 'varindexed ::= DOLLARID arrayindex',
        'varindexed ::= varvar arrayindex', 'arrayindex ::= arrayindex indexdef', 'arrayindex ::=',
        'indexdef ::= DOT DOLLARID', 'indexdef ::= DOT varvar', 'indexdef ::= DOT varvar AT ID', 'indexdef ::= DOT ID',
        'indexdef ::= DOT INTEGER', 'indexdef ::= DOT LDEL expr RDEL', 'indexdef ::= OPENB ID CLOSEB',
        'indexdef ::= OPENB ID DOT ID CLOSEB', 'indexdef ::= OPENB SINGLEQUOTESTRING CLOSEB',
        'indexdef ::= OPENB INTEGER CLOSEB', 'indexdef ::= OPENB DOLLARID CLOSEB', 'indexdef ::= OPENB variable CLOSEB',
        'indexdef ::= OPENB value CLOSEB', 'indexdef ::= OPENB expr CLOSEB', 'indexdef ::= OPENB CLOSEB',
        'varvar ::= DOLLARID', 'varvar ::= DOLLAR', 'varvar ::= varvar varvarele', 'varvarele ::= ID',
        'varvarele ::= LDEL expr RDEL', 'object ::= varindexed objectchain', 'objectchain ::= objectelement',
        'objectchain ::= objectchain objectelement', 'objectelement ::= PTR ID arrayindex',
        'objectelement ::= PTR varvar arrayindex', 'objectelement ::= PTR LDEL expr RDEL arrayindex',
        'objectelement ::= PTR ID LDEL expr RDEL arrayindex', 'objectelement ::= PTR method',
        'function ::= ns1 OPENP params CLOSEP', 'method ::= ID OPENP params CLOSEP',
        'method ::= DOLLARID OPENP params CLOSEP', 'params ::= params COMMA expr', 'params ::= expr', 'params ::=',
        'modifierlist ::= modifierlist modifier modparameters', 'modifierlist ::= modifier modparameters',
        'modifier ::= VERT AT ID', 'modifier ::= VERT ID', 'modparameters ::= modparameters modparameter',
        'modparameters ::=', 'modparameter ::= COLON value', 'modparameter ::= COLON array',
        'static_class_access ::= method', 'static_class_access ::= method objectchain', 'static_class_access ::= ID',
        'static_class_access ::= DOLLARID arrayindex', 'static_class_access ::= DOLLARID arrayindex objectchain',
        'lop ::= LOGOP', 'lop ::= TLOGOP', 'scond ::= SINGLECOND', 'array ::= OPENB arrayelements CLOSEB',
        'arrayelements ::= arrayelement', 'arrayelements ::= arrayelements COMMA arrayelement', 'arrayelements ::=',
        'arrayelement ::= value APTR expr', 'arrayelement ::= ID APTR expr', 'arrayelement ::= expr',
        'doublequoted_with_quotes ::= QUOTE QUOTE', 'doublequoted_with_quotes ::= QUOTE doublequoted QUOTE',
        'doublequoted ::= doublequoted doublequotedcontent', 'doublequoted ::= doublequotedcontent',
        'doublequotedcontent ::= BACKTICK variable BACKTICK', 'doublequotedcontent ::= BACKTICK expr BACKTICK',
        'doublequotedcontent ::= DOLLARID', 'doublequotedcontent ::= LDEL variable RDEL',
        'doublequotedcontent ::= LDEL expr RDEL', 'doublequotedcontent ::= smartytag', 'doublequotedcontent ::= TEXT',);

    public function tokenName($tokenType)
    {
        if ($tokenType === 0) {
            return 'End of Input';
        }
        if ($tokenType > 0 && $tokenType < count($this->yyTokenName)) {
            return $this->yyTokenName[$tokenType];
        } else {
            return "Unknown";
        }
    }

    public static function yy_destructor($yymajor, $yypminor)
    {
        switch ($yymajor) {
            default:
                break;   /* If no destructor action specified: do nothing */
        }
    }

    public function yy_pop_parser_stack()
    {
        if (empty($this->yystack)) {
            return;
        }
        $yytos = array_pop($this->yystack);
        if ($this->yyTraceFILE && $this->yyidx >= 0) {
            fwrite($this->yyTraceFILE, $this->yyTracePrompt . 'Popping ' . $this->yyTokenName[$yytos->major] . "\n");
        }
        $yymajor = $yytos->major;
        self::yy_destructor($yymajor, $yytos->minor);
        $this->yyidx --;

        return $yymajor;
    }

    public function __destruct()
    {
        while ($this->yystack !== Array()) {
            $this->yy_pop_parser_stack();
        }
        if (is_resource($this->yyTraceFILE)) {
            fclose($this->yyTraceFILE);
        }
    }

    public function yy_get_expected_tokens($token)
    {
        static $res3 = array();
        static $res4 = array();
        $state = $this->yystack[$this->yyidx]->stateno;
        $expected = self::$yyExpectedTokens[$state];
        if (isset($res3[$state][$token])) {
            if ($res3[$state][$token]) {
                return $expected;
            }
        } else {
            if ($res3[$state][$token] = in_array($token, self::$yyExpectedTokens[$state], true)) {
                return $expected;
            }
        }
        $stack = $this->yystack;
        $yyidx = $this->yyidx;
        do {
            $yyact = $this->yy_find_shift_action($token);
            if ($yyact >= self::YYNSTATE && $yyact < self::YYNSTATE + self::YYNRULE) {
                // reduce action
                $done = 0;
                do {
                    if ($done ++ == 100) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // too much recursion prevents proper detection
                        // so give up
                        return array_unique($expected);
                    }
                    $yyruleno = $yyact - self::YYNSTATE;
                    $this->yyidx -= self::$yyRuleInfo[$yyruleno][1];
                    $nextstate = $this->yy_find_reduce_action($this->yystack[$this->yyidx]->stateno, self::$yyRuleInfo[$yyruleno][0]);
                    if (isset(self::$yyExpectedTokens[$nextstate])) {
                        $expected = array_merge($expected, self::$yyExpectedTokens[$nextstate]);
                        if (isset($res4[$nextstate][$token])) {
                            if ($res4[$nextstate][$token]) {
                                $this->yyidx = $yyidx;
                                $this->yystack = $stack;
                                return array_unique($expected);
                            }
                        } else {
                            if ($res4[$nextstate][$token] = in_array($token, self::$yyExpectedTokens[$nextstate], true)) {
                                $this->yyidx = $yyidx;
                                $this->yystack = $stack;
                                return array_unique($expected);
                            }
                        }
                    }
                    if ($nextstate < self::YYNSTATE) {
                        // we need to shift a non-terminal
                        $this->yyidx ++;
                        $x = new TP_yyStackEntry;
                        $x->stateno = $nextstate;
                        $x->major = self::$yyRuleInfo[$yyruleno][0];
                        $this->yystack[$this->yyidx] = $x;
                        continue 2;
                    } elseif ($nextstate == self::YYNSTATE + self::YYNRULE + 1) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // the last token was just ignored, we can't accept
                        // by ignoring input, this is in essence ignoring a
                        // syntax error!
                        return array_unique($expected);
                    } elseif ($nextstate === self::YY_NO_ACTION) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // input accepted, but not shifted (I guess)
                        return $expected;
                    } else {
                        $yyact = $nextstate;
                    }
                } while (true);
            }
            break;
        } while (true);
        $this->yyidx = $yyidx;
        $this->yystack = $stack;

        return array_unique($expected);
    }

    public function yy_is_expected_token($token)
    {
        static $res = array();
        static $res2 = array();
        if ($token === 0) {
            return true; // 0 is not part of this
        }
        $state = $this->yystack[$this->yyidx]->stateno;
        if (isset($res[$state][$token])) {
            if ($res[$state][$token]) {
                return true;
            }
        } else {
            if ($res[$state][$token] = in_array($token, self::$yyExpectedTokens[$state], true)) {
                return true;
            }
        }
        $stack = $this->yystack;
        $yyidx = $this->yyidx;
        do {
            $yyact = $this->yy_find_shift_action($token);
            if ($yyact >= self::YYNSTATE && $yyact < self::YYNSTATE + self::YYNRULE) {
                // reduce action
                $done = 0;
                do {
                    if ($done ++ == 100) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // too much recursion prevents proper detection
                        // so give up
                        return true;
                    }
                    $yyruleno = $yyact - self::YYNSTATE;
                    $this->yyidx -= self::$yyRuleInfo[$yyruleno][1];
                    $nextstate = $this->yy_find_reduce_action($this->yystack[$this->yyidx]->stateno, self::$yyRuleInfo[$yyruleno][0]);
                    if (isset($res2[$nextstate][$token])) {
                        if ($res2[$nextstate][$token]) {
                            $this->yyidx = $yyidx;
                            $this->yystack = $stack;
                            return true;
                        }
                    } else {
                        if ($res2[$nextstate][$token] = (isset(self::$yyExpectedTokens[$nextstate]) && in_array($token, self::$yyExpectedTokens[$nextstate], true))) {
                            $this->yyidx = $yyidx;
                            $this->yystack = $stack;
                            return true;
                        }
                    }
                    if ($nextstate < self::YYNSTATE) {
                        // we need to shift a non-terminal
                        $this->yyidx ++;
                        $x = new TP_yyStackEntry;
                        $x->stateno = $nextstate;
                        $x->major = self::$yyRuleInfo[$yyruleno][0];
                        $this->yystack[$this->yyidx] = $x;
                        continue 2;
                    } elseif ($nextstate == self::YYNSTATE + self::YYNRULE + 1) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        if (!$token) {
                            // end of input: this is valid
                            return true;
                        }
                        // the last token was just ignored, we can't accept
                        // by ignoring input, this is in essence ignoring a
                        // syntax error!
                        return false;
                    } elseif ($nextstate === self::YY_NO_ACTION) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // input accepted, but not shifted (I guess)
                        return true;
                    } else {
                        $yyact = $nextstate;
                    }
                } while (true);
            }
            break;
        } while (true);
        $this->yyidx = $yyidx;
        $this->yystack = $stack;

        return true;
    }

    public function yy_find_shift_action($iLookAhead)
    {
        $stateno = $this->yystack[$this->yyidx]->stateno;

        /* if ($this->yyidx < 0) return self::YY_NO_ACTION;  */
        if (!isset(self::$yy_shift_ofst[$stateno])) {
            // no shift actions
            return self::$yy_default[$stateno];
        }
        $i = self::$yy_shift_ofst[$stateno];
        if ($i === self::YY_SHIFT_USE_DFLT) {
            return self::$yy_default[$stateno];
        }
        if ($iLookAhead == self::YYNOCODE) {
            return self::YY_NO_ACTION;
        }
        $i += $iLookAhead;
        if ($i < 0 || $i >= self::YY_SZ_ACTTAB || self::$yy_lookahead[$i] != $iLookAhead) {
            if (count(self::$yyFallback) && $iLookAhead < count(self::$yyFallback) && ($iFallback = self::$yyFallback[$iLookAhead]) != 0) {
                if ($this->yyTraceFILE) {
                    fwrite($this->yyTraceFILE, $this->yyTracePrompt . "FALLBACK " . $this->yyTokenName[$iLookAhead] . " => " . $this->yyTokenName[$iFallback] . "\n");
                }

                return $this->yy_find_shift_action($iFallback);
            }

            return self::$yy_default[$stateno];
        } else {
            return self::$yy_action[$i];
        }
    }

    public function yy_find_reduce_action($stateno, $iLookAhead)
    {
        /* $stateno = $this->yystack[$this->yyidx]->stateno; */

        if (!isset(self::$yy_reduce_ofst[$stateno])) {
            return self::$yy_default[$stateno];
        }
        $i = self::$yy_reduce_ofst[$stateno];
        if ($i == self::YY_REDUCE_USE_DFLT) {
            return self::$yy_default[$stateno];
        }
        if ($iLookAhead == self::YYNOCODE) {
            return self::YY_NO_ACTION;
        }
        $i += $iLookAhead;
        if ($i < 0 || $i >= self::YY_SZ_ACTTAB || self::$yy_lookahead[$i] != $iLookAhead) {
            return self::$yy_default[$stateno];
        } else {
            return self::$yy_action[$i];
        }
    }

    public function yy_shift($yyNewState, $yyMajor, $yypMinor)
    {
        $this->yyidx ++;
        if ($this->yyidx >= self::YYSTACKDEPTH) {
            $this->yyidx --;
            if ($this->yyTraceFILE) {
                fprintf($this->yyTraceFILE, "%sStack Overflow!\n", $this->yyTracePrompt);
            }
            while ($this->yyidx >= 0) {
                $this->yy_pop_parser_stack();
            }
            #line 190 "../smarty/lexer/smarty_internal_templateparser.y"

            $this->internalError = true;
            $this->compiler->trigger_template_error("Stack overflow in template parser");

            return;
        }
        $yytos = new TP_yyStackEntry;
        $yytos->stateno = $yyNewState;
        $yytos->major = $yyMajor;
        $yytos->minor = $yypMinor;
        $this->yystack[] = $yytos;
        if ($this->yyTraceFILE && $this->yyidx > 0) {
            fprintf($this->yyTraceFILE, "%sShift %d\n", $this->yyTracePrompt, $yyNewState);
            fprintf($this->yyTraceFILE, "%sStack:", $this->yyTracePrompt);
            for ($i = 1; $i <= $this->yyidx; $i ++) {
                fprintf($this->yyTraceFILE, " %s", $this->yyTokenName[$this->yystack[$i]->major]);
            }
            fwrite($this->yyTraceFILE, "\n");
        }
    }

    public static $yyRuleInfo = array(array(0 => 62, 1 => 1), array(0 => 63, 1 => 1), array(0 => 63, 1 => 2),
        array(0 => 63, 1 => 0), array(0 => 64, 1 => 1), array(0 => 64, 1 => 1), array(0 => 64, 1 => 1),
        array(0 => 64, 1 => 1), array(0 => 64, 1 => 1), array(0 => 67, 1 => 1), array(0 => 67, 1 => 2),
        array(0 => 64, 1 => 1), array(0 => 64, 1 => 1), array(0 => 64, 1 => 1), array(0 => 66, 1 => 2),
        array(0 => 66, 1 => 3), array(0 => 68, 1 => 2), array(0 => 68, 1 => 0), array(0 => 69, 1 => 1),
        array(0 => 69, 1 => 1), array(0 => 65, 1 => 2), array(0 => 65, 1 => 1), array(0 => 70, 1 => 2),
        array(0 => 70, 1 => 3), array(0 => 70, 1 => 2), array(0 => 70, 1 => 3), array(0 => 70, 1 => 2),
        array(0 => 70, 1 => 3), array(0 => 70, 1 => 4), array(0 => 70, 1 => 4), array(0 => 70, 1 => 5),
        array(0 => 70, 1 => 5), array(0 => 65, 1 => 1), array(0 => 70, 1 => 3), array(0 => 70, 1 => 2),
        array(0 => 70, 1 => 4), array(0 => 70, 1 => 5), array(0 => 70, 1 => 6), array(0 => 70, 1 => 2),
        array(0 => 70, 1 => 3), array(0 => 70, 1 => 2), array(0 => 70, 1 => 3), array(0 => 70, 1 => 8),
        array(0 => 79, 1 => 2), array(0 => 79, 1 => 1), array(0 => 70, 1 => 5), array(0 => 70, 1 => 7),
        array(0 => 70, 1 => 2), array(0 => 70, 1 => 6), array(0 => 70, 1 => 8), array(0 => 70, 1 => 6),
        array(0 => 70, 1 => 8), array(0 => 70, 1 => 3), array(0 => 70, 1 => 4), array(0 => 70, 1 => 2),
        array(0 => 65, 1 => 1), array(0 => 70, 1 => 2), array(0 => 70, 1 => 3), array(0 => 70, 1 => 4),
        array(0 => 70, 1 => 5), array(0 => 72, 1 => 2), array(0 => 72, 1 => 1), array(0 => 72, 1 => 0),
        array(0 => 82, 1 => 4), array(0 => 82, 1 => 2), array(0 => 82, 1 => 2), array(0 => 82, 1 => 2),
        array(0 => 82, 1 => 2), array(0 => 82, 1 => 2), array(0 => 82, 1 => 4), array(0 => 78, 1 => 1),
        array(0 => 78, 1 => 3), array(0 => 77, 1 => 3), array(0 => 77, 1 => 3), array(0 => 77, 1 => 3),
        array(0 => 77, 1 => 3), array(0 => 74, 1 => 1), array(0 => 74, 1 => 1), array(0 => 74, 1 => 3),
        array(0 => 74, 1 => 3), array(0 => 74, 1 => 3), array(0 => 74, 1 => 1), array(0 => 74, 1 => 2),
        array(0 => 74, 1 => 3), array(0 => 74, 1 => 2), array(0 => 74, 1 => 3), array(0 => 74, 1 => 3),
        array(0 => 74, 1 => 3), array(0 => 83, 1 => 7), array(0 => 83, 1 => 7), array(0 => 73, 1 => 1),
        array(0 => 73, 1 => 2), array(0 => 73, 1 => 2), array(0 => 73, 1 => 2), array(0 => 73, 1 => 2),
        array(0 => 73, 1 => 1), array(0 => 73, 1 => 1), array(0 => 73, 1 => 3), array(0 => 73, 1 => 2),
        array(0 => 73, 1 => 2), array(0 => 73, 1 => 1), array(0 => 73, 1 => 1), array(0 => 73, 1 => 3),
        array(0 => 73, 1 => 1), array(0 => 73, 1 => 1), array(0 => 73, 1 => 3), array(0 => 73, 1 => 1),
        array(0 => 73, 1 => 2), array(0 => 73, 1 => 1), array(0 => 73, 1 => 3), array(0 => 87, 1 => 1),
        array(0 => 87, 1 => 1), array(0 => 71, 1 => 1), array(0 => 71, 1 => 1), array(0 => 71, 1 => 3),
        array(0 => 71, 1 => 1), array(0 => 71, 1 => 3), array(0 => 71, 1 => 4), array(0 => 71, 1 => 3),
        array(0 => 71, 1 => 4), array(0 => 75, 1 => 2), array(0 => 75, 1 => 2), array(0 => 92, 1 => 2),
        array(0 => 92, 1 => 0), array(0 => 93, 1 => 2), array(0 => 93, 1 => 2), array(0 => 93, 1 => 4),
        array(0 => 93, 1 => 2), array(0 => 93, 1 => 2), array(0 => 93, 1 => 4), array(0 => 93, 1 => 3),
        array(0 => 93, 1 => 5), array(0 => 93, 1 => 3), array(0 => 93, 1 => 3), array(0 => 93, 1 => 3),
        array(0 => 93, 1 => 3), array(0 => 93, 1 => 3), array(0 => 93, 1 => 3), array(0 => 93, 1 => 2),
        array(0 => 80, 1 => 1), array(0 => 80, 1 => 1), array(0 => 80, 1 => 2), array(0 => 94, 1 => 1),
        array(0 => 94, 1 => 3), array(0 => 91, 1 => 2), array(0 => 95, 1 => 1), array(0 => 95, 1 => 2),
        array(0 => 96, 1 => 3), array(0 => 96, 1 => 3), array(0 => 96, 1 => 5), array(0 => 96, 1 => 6),
        array(0 => 96, 1 => 2), array(0 => 88, 1 => 4), array(0 => 97, 1 => 4), array(0 => 97, 1 => 4),
        array(0 => 98, 1 => 3), array(0 => 98, 1 => 1), array(0 => 98, 1 => 0), array(0 => 76, 1 => 3),
        array(0 => 76, 1 => 2), array(0 => 99, 1 => 3), array(0 => 99, 1 => 2), array(0 => 81, 1 => 2),
        array(0 => 81, 1 => 0), array(0 => 100, 1 => 2), array(0 => 100, 1 => 2), array(0 => 90, 1 => 1),
        array(0 => 90, 1 => 2), array(0 => 90, 1 => 1), array(0 => 90, 1 => 2), array(0 => 90, 1 => 3),
        array(0 => 85, 1 => 1), array(0 => 85, 1 => 1), array(0 => 86, 1 => 1), array(0 => 84, 1 => 3),
        array(0 => 101, 1 => 1), array(0 => 101, 1 => 3), array(0 => 101, 1 => 0), array(0 => 102, 1 => 3),
        array(0 => 102, 1 => 3), array(0 => 102, 1 => 1), array(0 => 89, 1 => 2), array(0 => 89, 1 => 3),
        array(0 => 103, 1 => 2), array(0 => 103, 1 => 1), array(0 => 104, 1 => 3), array(0 => 104, 1 => 3),
        array(0 => 104, 1 => 1), array(0 => 104, 1 => 3), array(0 => 104, 1 => 3), array(0 => 104, 1 => 1),
        array(0 => 104, 1 => 1),);

    public static $yyReduceMap = array(0   => 0, 1 => 1, 2 => 2, 4 => 4, 5 => 5, 6 => 6, 7 => 7, 8 => 8, 9 => 9,
                                       18  => 9, 19 => 9, 44 => 9, 67 => 9, 68 => 9, 76 => 9, 77 => 9, 81 => 9, 90 => 9,
                                       95  => 9, 96 => 9, 101 => 9, 103 => 9, 104 => 9, 108 => 9, 110 => 9, 111 => 9,
                                       115 => 9, 175 => 9, 180 => 9, 10 => 10, 11 => 11, 12 => 12, 13 => 13, 14 => 14,
                                       17  => 14, 15 => 15, 75 => 15, 16 => 16, 91 => 16, 93 => 16, 94 => 16, 122 => 16,
                                       20  => 20, 21 => 21, 22 => 22, 24 => 22, 26 => 22, 23 => 23, 25 => 23, 27 => 23,
                                       28  => 28, 29 => 28, 30 => 30, 31 => 31, 32 => 32, 33 => 33, 34 => 34, 35 => 35,
                                       36  => 36, 37 => 37, 38 => 38, 39 => 39, 41 => 39, 40 => 40, 42 => 42, 43 => 43,
                                       45  => 45, 46 => 46, 47 => 47, 48 => 48, 50 => 48, 49 => 49, 51 => 49, 52 => 52,
                                       53  => 53, 54 => 54, 55 => 55, 56 => 56, 57 => 57, 58 => 58, 59 => 59, 60 => 60,
                                       61  => 61, 70 => 61, 156 => 61, 160 => 61, 164 => 61, 165 => 61, 62 => 62,
                                       157 => 62, 163 => 62, 63 => 63, 64 => 64, 65 => 64, 66 => 66, 142 => 66,
                                       69  => 69, 71 => 71, 72 => 72, 73 => 72, 74 => 74, 78 => 78, 79 => 79, 80 => 79,
                                       82  => 82, 107 => 82, 83 => 83, 84 => 84, 85 => 85, 86 => 86, 87 => 87, 88 => 88,
                                       89  => 89, 92 => 92, 97 => 97, 98 => 98, 99 => 99, 100 => 100, 102 => 102,
                                       105 => 105, 106 => 106, 109 => 109, 112 => 112, 113 => 113, 114 => 114,
                                       116 => 116, 117 => 117, 118 => 118, 119 => 119, 120 => 120, 121 => 121,
                                       123 => 123, 177 => 123, 124 => 124, 125 => 125, 126 => 126, 127 => 127,
                                       128 => 128, 129 => 129, 137 => 129, 130 => 130, 131 => 131, 132 => 132,
                                       133 => 132, 135 => 132, 136 => 132, 134 => 134, 138 => 138, 139 => 139,
                                       140 => 140, 181 => 140, 141 => 141, 143 => 143, 144 => 144, 145 => 145,
                                       146 => 146, 147 => 147, 148 => 148, 149 => 149, 150 => 150, 151 => 151,
                                       152 => 152, 153 => 153, 154 => 154, 155 => 155, 158 => 158, 159 => 159,
                                       161 => 161, 162 => 162, 166 => 166, 167 => 167, 168 => 168, 169 => 169,
                                       170 => 170, 171 => 171, 172 => 172, 173 => 173, 174 => 174, 176 => 176,
                                       178 => 178, 179 => 179, 182 => 182, 183 => 183, 184 => 184, 185 => 185,
                                       186 => 185, 188 => 185, 187 => 187, 189 => 189, 190 => 190, 191 => 191,);

    #line 201 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r0()
    {
        $this->_retvalue = $this->root_buffer->to_smarty_php();
    }

    #line 209 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r1()
    {
        if ($this->yystack[$this->yyidx + 0]->minor != null) {
            $this->current_buffer->append_subtree($this->yystack[$this->yyidx + 0]->minor);
        }
    }

    #line 216 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r2()
    {
        if ($this->yystack[$this->yyidx + 0]->minor != null) {
            // because of possible code injection
            $this->current_buffer->append_subtree($this->yystack[$this->yyidx + 0]->minor);
        }
    }

    #line 230 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r4()
    {
        if ($this->compiler->has_code) {
            $this->_retvalue = $this->mergePrefixCode($this->yystack[$this->yyidx + 0]->minor);
        } else {
            $this->_retvalue = null;
        }
        $this->compiler->has_variable_string = false;
        $this->block_nesting_level = count($this->compiler->_tag_stack);
    }

    #line 241 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r5()
    {
        $this->_retvalue = new Smarty_Internal_ParseTree_Text($this, $this->yystack[$this->yyidx + 0]->minor);
    }

    #line 245 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r6()
    {
        $code = $this->compiler->compileTag('private_php', array(array('code' => $this->yystack[$this->yyidx + 0]->minor),
            array('type' => $this->lex->phpType)), array());
        if ($this->compiler->has_code && !empty($code)) {
            $tmp = '';
            foreach ($this->compiler->prefix_code as $code) {
                $tmp .= $code;
            }
            $this->compiler->prefix_code = array();
            $this->_retvalue = new Smarty_Internal_ParseTree_Tag($this, $this->compiler->processNocacheCode($tmp . $code, true));
        } else {
            $this->_retvalue = null;
        }
    }

    #line 256 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r7()
    {
        $this->compiler->tag_nocache = true;
        $save = $this->template->has_nocache_code;
        $this->_retvalue = new Smarty_Internal_ParseTree_Tag($this, $this->compiler->processNocacheCode("<?php echo '{$this->yystack[$this->yyidx + 0]->minor}';?>\n", $this->compiler, true));
        $this->template->has_nocache_code = $save;
    }

    #line 263 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r8()
    {
        $this->_retvalue = $this->compiler->processText($this->yystack[$this->yyidx + 0]->minor);
    }

    #line 267 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r9()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 271 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r10()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 1]->minor . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 276 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r11()
    {
        $this->strip = true;
    }

    #line 280 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r12()
    {
        $this->strip = false;
    }

    #line 284 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r13()
    {
        if ($this->strip) {
            SMARTY_INTERNAL_COMPILE_BLOCK::blockSource($this->compiler, preg_replace('![\t ]*[\r\n]+[\t ]*!', '', $this->yystack[$this->yyidx + 0]->minor));
        } else {
            SMARTY_INTERNAL_COMPILE_BLOCK::blockSource($this->compiler, $this->yystack[$this->yyidx + 0]->minor);
        }
    }

    #line 293 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r14()
    {
        $this->_retvalue = '';
    }

    #line 297 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r15()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 1]->minor;
    }

    #line 301 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r16()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 1]->minor . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 317 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r20()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 1]->minor;
    }

    #line 323 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r21()
    {
        $var = trim(substr($this->yystack[$this->yyidx + 0]->minor, $this->lex->ldel_length, - $this->lex->rdel_length), ' $');
        if (preg_match('/^(.*)(\s+nocache)$/', $var, $match)) {
            $this->_retvalue = $this->compiler->compileTag('private_print_expression', array('nocache'), array('value' => $this->compiler->compileVariable('\'' . $match[1] . '\'')));
        } else {
            $this->_retvalue = $this->compiler->compileTag('private_print_expression', array(), array('value' => $this->compiler->compileVariable('\'' . $var . '\'')));
        }
    }

    #line 333 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r22()
    {
        $this->_retvalue = $this->compiler->compileTag('private_print_expression', array(), array('value' => $this->yystack[$this->yyidx + 0]->minor));
    }

    #line 337 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r23()
    {
        $this->_retvalue = $this->compiler->compileTag('private_print_expression', $this->yystack[$this->yyidx + 0]->minor, array('value' => $this->yystack[$this->yyidx + - 1]->minor));
    }

    #line 360 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r28()
    {
        $this->_retvalue = $this->compiler->compileTag('assign', array(array('value' => $this->yystack[$this->yyidx + 0]->minor),
            array('var' => '\'' . substr($this->yystack[$this->yyidx + - 2]->minor, 1) . '\'')));
    }

    #line 368 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r30()
    {
        $this->_retvalue = $this->compiler->compileTag('assign', array_merge(array(array('value' => $this->yystack[$this->yyidx + - 1]->minor),
                                                                                 array('var' => '\'' . substr($this->yystack[$this->yyidx + - 3]->minor, 1) . '\'')), $this->yystack[$this->yyidx + 0]->minor));
    }

    #line 372 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r31()
    {
        $this->_retvalue = $this->compiler->compileTag('assign', array_merge(array(array('value' => $this->yystack[$this->yyidx + - 1]->minor),
                                                                                 array('var' => $this->yystack[$this->yyidx + - 3]->minor['var'])), $this->yystack[$this->yyidx + 0]->minor), array('smarty_internal_index' => $this->yystack[$this->yyidx + - 3]->minor['smarty_internal_index']));
    }

    #line 377 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r32()
    {
        $tag = trim(substr($this->yystack[$this->yyidx + 0]->minor, $this->lex->ldel_length, - $this->lex->rdel_length));
        if ($tag == 'strip') {
            $this->strip = true;
            $this->_retvalue = null;;
        } else {
            if (defined($tag)) {
                if ($this->security) {
                    $this->security->isTrustedConstant($tag, $this->compiler);
                }
                $this->_retvalue = $this->compiler->compileTag('private_print_expression', array(), array('value' => $tag));
            } else {
                if (preg_match('/^(.*)(\s+nocache)$/', $tag, $match)) {
                    $this->_retvalue = $this->compiler->compileTag($match[1], array("'nocache'"));
                } else {
                    $this->_retvalue = $this->compiler->compileTag($tag, array());
                }
            }
        }
    }

    #line 399 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r33()
    {
        if (defined($this->yystack[$this->yyidx + - 1]->minor)) {
            if ($this->security) {
                $this->security->isTrustedConstant($this->yystack[$this->yyidx + - 1]->minor, $this->compiler);
            }
            $this->_retvalue = $this->compiler->compileTag('private_print_expression', $this->yystack[$this->yyidx + 0]->minor, array('value' => $this->yystack[$this->yyidx + - 1]->minor));
        } else {
            $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + - 1]->minor, $this->yystack[$this->yyidx + 0]->minor);
        }
    }

    #line 409 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r34()
    {
        if (defined($this->yystack[$this->yyidx + 0]->minor)) {
            if ($this->security) {
                $this->security->isTrustedConstant($this->yystack[$this->yyidx + 0]->minor, $this->compiler);
            }
            $this->_retvalue = $this->compiler->compileTag('private_print_expression', array(), array('value' => $this->yystack[$this->yyidx + 0]->minor));
        } else {
            $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + 0]->minor, array());
        }
    }

    #line 422 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r35()
    {
        if (defined($this->yystack[$this->yyidx + - 2]->minor)) {
            if ($this->security) {
                $this->security->isTrustedConstant($this->yystack[$this->yyidx + - 2]->minor, $this->compiler);
            }
            $this->_retvalue = $this->compiler->compileTag('private_print_expression', $this->yystack[$this->yyidx + 0]->minor, array('value'        => $this->yystack[$this->yyidx + - 2]->minor,
                                                                                                                                      'modifierlist' => $this->yystack[$this->yyidx + - 1]->minor));
        } else {
            $this->_retvalue = '<?php ob_start();?>' . $this->compiler->compileTag($this->yystack[$this->yyidx + - 2]->minor, $this->yystack[$this->yyidx + 0]->minor) . '<?php echo ';
            $this->_retvalue .= $this->compiler->compileTag('private_modifier', array(), array('modifierlist' => $this->yystack[$this->yyidx + - 1]->minor,
                                                                                               'value'        => 'ob_get_clean()')) . ';?>';
        }
    }

    #line 435 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r36()
    {
        $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + - 3]->minor, $this->yystack[$this->yyidx + 0]->minor, array('object_method' => $this->yystack[$this->yyidx + - 1]->minor));
    }

    #line 440 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r37()
    {
        $this->_retvalue = '<?php ob_start();?>' . $this->compiler->compileTag($this->yystack[$this->yyidx + - 4]->minor, $this->yystack[$this->yyidx + 0]->minor, array('object_method' => $this->yystack[$this->yyidx + - 2]->minor)) . '<?php echo ';
        $this->_retvalue .= $this->compiler->compileTag('private_modifier', array(), array('modifierlist' => $this->yystack[$this->yyidx + - 1]->minor,
                                                                                           'value'        => 'ob_get_clean()')) . ';?>';
    }

    #line 446 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r38()
    {
        $tag = trim(substr($this->yystack[$this->yyidx + - 1]->minor, $this->lex->ldel_length));
        $this->_retvalue = $this->compiler->compileTag(($tag == 'else if') ? 'elseif' : $tag, array(), array('if condition' => $this->yystack[$this->yyidx + 0]->minor));
    }

    #line 451 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r39()
    {
        $tag = trim(substr($this->yystack[$this->yyidx + - 2]->minor, $this->lex->ldel_length));
        $this->_retvalue = $this->compiler->compileTag(($tag == 'else if') ? 'elseif' : $tag, $this->yystack[$this->yyidx + 0]->minor, array('if condition' => $this->yystack[$this->yyidx + - 1]->minor));
    }

    #line 456 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r40()
    {
        $tag = trim(substr($this->yystack[$this->yyidx + - 1]->minor, $this->lex->ldel_length));
        $this->_retvalue = $this->compiler->compileTag(($tag == 'else if') ? 'elseif' : $tag, array(), array('if condition' => $this->yystack[$this->yyidx + 0]->minor));
    }

    #line 467 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r42()
    {
        $this->_retvalue = $this->compiler->compileTag('for', array_merge($this->yystack[$this->yyidx + 0]->minor, array(array('start' => $this->yystack[$this->yyidx + - 6]->minor),
            array('ifexp' => $this->yystack[$this->yyidx + - 4]->minor),
            array('var' => $this->yystack[$this->yyidx + - 2]->minor),
            array('step' => $this->yystack[$this->yyidx + - 1]->minor))), 1);
    }

    #line 471 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r43()
    {
        $this->_retvalue = '=' . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 479 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r45()
    {
        $this->_retvalue = $this->compiler->compileTag('for', array_merge($this->yystack[$this->yyidx + 0]->minor, array(array('start' => $this->yystack[$this->yyidx + - 3]->minor),
            array('to' => $this->yystack[$this->yyidx + - 1]->minor))), 0);
    }

    #line 483 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r46()
    {
        $this->_retvalue = $this->compiler->compileTag('for', array_merge($this->yystack[$this->yyidx + 0]->minor, array(array('start' => $this->yystack[$this->yyidx + - 5]->minor),
            array('to' => $this->yystack[$this->yyidx + - 3]->minor),
            array('step' => $this->yystack[$this->yyidx + - 1]->minor))), 0);
    }

    #line 488 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r47()
    {
        $this->_retvalue = $this->compiler->compileTag('foreach', $this->yystack[$this->yyidx + 0]->minor);
    }

    #line 493 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r48()
    {
        $this->_retvalue = $this->compiler->compileTag('foreach', array_merge($this->yystack[$this->yyidx + 0]->minor, array(array('from' => $this->yystack[$this->yyidx + - 3]->minor),
            array('item' => $this->yystack[$this->yyidx + - 1]->minor))));
    }

    #line 497 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r49()
    {
        $this->_retvalue = $this->compiler->compileTag('foreach', array_merge($this->yystack[$this->yyidx + 0]->minor, array(array('from' => $this->yystack[$this->yyidx + - 5]->minor),
            array('item' => $this->yystack[$this->yyidx + - 1]->minor),
            array('key' => $this->yystack[$this->yyidx + - 3]->minor))));
    }

    #line 510 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r52()
    {
        $this->_retvalue = $this->compiler->compileTag('setfilter', array(), array('modifier_list' => array(array_merge(array($this->yystack[$this->yyidx + - 1]->minor), $this->yystack[$this->yyidx + 0]->minor))));
    }

    #line 514 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r53()
    {
        $this->_retvalue = $this->compiler->compileTag('setfilter', array(), array('modifier_list' => array_merge(array(array_merge(array($this->yystack[$this->yyidx + - 2]->minor), $this->yystack[$this->yyidx + - 1]->minor)), $this->yystack[$this->yyidx + 0]->minor)));
    }

    #line 519 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r54()
    {
        $j = strrpos($this->yystack[$this->yyidx + 0]->minor, '.');
        if ($this->yystack[$this->yyidx + 0]->minor[$j + 1] == 'c') {
            // {$smarty.block.child}
            $this->_retvalue = SMARTY_INTERNAL_COMPILE_BLOCK::compileChildBlock($this->compiler);
        } else {
            // {$smarty.block.parent}
            $this->_retvalue = SMARTY_INTERNAL_COMPILE_BLOCK::compileParentBlock($this->compiler);
        }
    }

    #line 532 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r55()
    {
        $tag = trim(substr($this->yystack[$this->yyidx + 0]->minor, $this->lex->ldel_length, - $this->lex->rdel_length), ' /');
        if ($tag == 'strip') {
            $this->strip = false;
            $this->_retvalue = null;
        } else {
            $this->_retvalue = $this->compiler->compileTag($tag . 'close', array());
        }
    }

    #line 541 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r56()
    {
        $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + 0]->minor . 'close', array());
    }

    #line 545 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r57()
    {
        $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + - 1]->minor . 'close', array(), array('modifier_list' => $this->yystack[$this->yyidx + 0]->minor));
    }

    #line 550 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r58()
    {
        $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + - 2]->minor . 'close', array(), array('object_method' => $this->yystack[$this->yyidx + 0]->minor));
    }

    #line 554 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r59()
    {
        $this->_retvalue = $this->compiler->compileTag($this->yystack[$this->yyidx + - 3]->minor . 'close', array(), array('object_method' => $this->yystack[$this->yyidx + - 1]->minor,
                                                                                                                           'modifier_list' => $this->yystack[$this->yyidx + 0]->minor));
    }

    #line 562 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r60()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 1]->minor;
        $this->_retvalue[] = $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 568 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r61()
    {
        $this->_retvalue = array($this->yystack[$this->yyidx + 0]->minor);
    }

    #line 573 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r62()
    {
        $this->_retvalue = array();
    }

    #line 578 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r63()
    {
        if (defined($this->yystack[$this->yyidx + 0]->minor)) {
            if ($this->security) {
                $this->security->isTrustedConstant($this->yystack[$this->yyidx + 0]->minor, $this->compiler);
            }
            $this->_retvalue = array($this->yystack[$this->yyidx + - 2]->minor => $this->yystack[$this->yyidx + 0]->minor);
        } else {
            $this->_retvalue = array($this->yystack[$this->yyidx + - 2]->minor => '\'' . $this->yystack[$this->yyidx + 0]->minor . '\'');
        }
    }

    #line 589 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r64()
    {
        $this->_retvalue = array(trim($this->yystack[$this->yyidx + - 1]->minor, " =\n\r\t") => $this->yystack[$this->yyidx + 0]->minor);
    }

    #line 597 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r66()
    {
        $this->_retvalue = '\'' . $this->yystack[$this->yyidx + 0]->minor . '\'';
    }

    #line 609 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r69()
    {
        $this->_retvalue = array($this->yystack[$this->yyidx + - 2]->minor => $this->yystack[$this->yyidx + 0]->minor);
    }

    #line 622 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r71()
    {
        $this->yystack[$this->yyidx + - 2]->minor[] = $this->yystack[$this->yyidx + 0]->minor;
        $this->_retvalue = $this->yystack[$this->yyidx + - 2]->minor;
    }

    #line 627 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r72()
    {
        $this->_retvalue = array('var'   => '\'' . substr($this->yystack[$this->yyidx + - 2]->minor, 1) . '\'',
                                 'value' => $this->yystack[$this->yyidx + 0]->minor);
    }

    #line 634 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r74()
    {
        $this->_retvalue = array('var'   => $this->yystack[$this->yyidx + - 2]->minor,
                                 'value' => $this->yystack[$this->yyidx + 0]->minor);
    }

    #line 658 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r78()
    {
        $this->_retvalue = '$_smarty_tpl->getStreamVariable(\'' . substr($this->yystack[$this->yyidx + - 2]->minor, 1) . '://' . $this->yystack[$this->yyidx + 0]->minor . '\')';
    }

    #line 663 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r79()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 2]->minor . trim($this->yystack[$this->yyidx + - 1]->minor) . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 677 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r82()
    {
        $this->_retvalue = $this->compiler->compileTag('private_modifier', array(), array('value'        => $this->yystack[$this->yyidx + - 1]->minor,
                                                                                          'modifierlist' => $this->yystack[$this->yyidx + 0]->minor));
    }

    #line 683 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r83()
    {
        $this->_retvalue = (isset($this->yystack[$this->yyidx + - 1]->minor['pre']) ? $this->yystack[$this->yyidx + - 1]->minor['pre'] : '') . $this->yystack[$this->yyidx + - 2]->minor . $this->yystack[$this->yyidx + - 1]->minor['op'] . $this->yystack[$this->yyidx + 0]->minor . (isset($this->yystack[$this->yyidx + - 1]->minor['pre']) ? ')' : '');
    }

    #line 686 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r84()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + 0]->minor . $this->yystack[$this->yyidx + - 1]->minor . ')';
    }

    #line 690 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r85()
    {
        $this->_retvalue = 'in_array(' . $this->yystack[$this->yyidx + - 2]->minor . ',' . $this->yystack[$this->yyidx + 0]->minor . ')';
    }

    #line 694 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r86()
    {
        $this->_retvalue = 'in_array(' . $this->yystack[$this->yyidx + - 2]->minor . ',(array)' . $this->yystack[$this->yyidx + 0]->minor . ')';
    }

    #line 698 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r87()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 2]->minor . $this->yystack[$this->yyidx + - 1]->minor . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 706 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r88()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 5]->minor . ' ? ' . $this->compiler->compileVariable('\'' . substr($this->yystack[$this->yyidx + - 2]->minor, 1) . '\'') . ' : ' . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 710 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r89()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 5]->minor . ' ? ' . $this->yystack[$this->yyidx + - 2]->minor . ' : ' . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 725 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r92()
    {
        $this->_retvalue = '!' . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 746 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r97()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 2]->minor . '.' . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 750 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r98()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 1]->minor . '.';
    }

    #line 754 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r99()
    {
        $this->_retvalue = '.' . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 759 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r100()
    {
        if (defined($this->yystack[$this->yyidx + 0]->minor)) {
            if ($this->security) {
                $this->security->isTrustedConstant($this->yystack[$this->yyidx + 0]->minor, $this->compiler);
            }
            $this->_retvalue = $this->yystack[$this->yyidx + 0]->minor;
        } else {
            $this->_retvalue = '\'' . $this->yystack[$this->yyidx + 0]->minor . '\'';
        }
    }

    #line 776 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r102()
    {
        $this->_retvalue = "(" . $this->yystack[$this->yyidx + - 1]->minor . ")";
    }

    #line 791 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r105()
    {
        self::$prefix_number ++;
        if ($this->yystack[$this->yyidx + - 2]->minor['var'] == '\'smarty\'') {
            $this->compiler->prefix_code[] = '<?php $_tmp' . self::$prefix_number . ' = ' . $this->compiler->compileTag('private_special_variable', array(), $this->yystack[$this->yyidx + - 2]->minor['smarty_internal_index']) . ';?>';
        } else {
            $this->compiler->prefix_code[] = '<?php $_tmp' . self::$prefix_number . ' = ' . $this->compiler->compileVariable($this->yystack[$this->yyidx + - 2]->minor['var']) . $this->yystack[$this->yyidx + - 2]->minor['smarty_internal_index'] . ';?>';
        }
        $this->_retvalue = '$_tmp' . self::$prefix_number . '::' . $this->yystack[$this->yyidx + 0]->minor[0] . $this->yystack[$this->yyidx + 0]->minor[1];
    }

    #line 802 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r106()
    {
        self::$prefix_number ++;
        $tmp = $this->compiler->appendCode('<?php ob_start();?>', $this->yystack[$this->yyidx + 0]->minor);
        $this->compiler->prefix_code[] = $this->compiler->appendCode($tmp, '<?php $_tmp' . self::$prefix_number . '=ob_get_clean();?>');
        $this->_retvalue = '$_tmp' . self::$prefix_number;
    }

    #line 819 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r109()
    {
        if (!in_array(strtolower($this->yystack[$this->yyidx + - 2]->minor), array('self',
                'parent')) && (!$this->security || $this->security->isTrustedStaticClassAccess($this->yystack[$this->yyidx + - 2]->minor, $this->yystack[$this->yyidx + 0]->minor, $this->compiler))
        ) {
            if (isset($this->smarty->registered_classes[$this->yystack[$this->yyidx + - 2]->minor])) {
                $this->_retvalue = $this->smarty->registered_classes[$this->yystack[$this->yyidx + - 2]->minor] . '::' . $this->yystack[$this->yyidx + 0]->minor[0] . $this->yystack[$this->yyidx + 0]->minor[1];
            } else {
                $this->_retvalue = $this->yystack[$this->yyidx + - 2]->minor . '::' . $this->yystack[$this->yyidx + 0]->minor[0] . $this->yystack[$this->yyidx + 0]->minor[1];
            }
        } else {
            $this->compiler->trigger_template_error("static class '" . $this->yystack[$this->yyidx + - 2]->minor . "' is undefined or not allowed by security setting");
        }
    }

    #line 853 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r112()
    {
        $this->_retvalue = $this->compiler->compileVariable('\'' . substr($this->yystack[$this->yyidx + 0]->minor, 1) . '\'');
    }

    #line 856 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r113()
    {
        if ($this->yystack[$this->yyidx + 0]->minor['var'] == '\'smarty\'') {
            $smarty_var = $this->compiler->compileTag('private_special_variable', array(), $this->yystack[$this->yyidx + 0]->minor['smarty_internal_index']);
            $this->_retvalue = $smarty_var;
        } else {
            // used for array reset,next,prev,end,current 
            $this->last_variable = $this->yystack[$this->yyidx + 0]->minor['var'];
            $this->last_index = $this->yystack[$this->yyidx + 0]->minor['smarty_internal_index'];
            $this->_retvalue = $this->compiler->compileVariable($this->yystack[$this->yyidx + 0]->minor['var']) . $this->yystack[$this->yyidx + 0]->minor['smarty_internal_index'];
        }
    }

    #line 869 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r114()
    {
        $this->_retvalue = '$_smarty_tpl->tpl_vars[' . $this->yystack[$this->yyidx + - 2]->minor . ']->' . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 879 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r116()
    {
        $this->_retvalue = '$_smarty_tpl->getConfigVariable( \'' . $this->yystack[$this->yyidx + - 1]->minor . '\')';
    }

    #line 883 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r117()
    {
        $this->_retvalue = '(is_array($tmp = $_smarty_tpl->getConfigVariable( \'' . $this->yystack[$this->yyidx + - 2]->minor . '\')) ? $tmp' . $this->yystack[$this->yyidx + 0]->minor . ' :null)';
    }

    #line 887 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r118()
    {
        $this->_retvalue = '$_smarty_tpl->getConfigVariable( ' . $this->yystack[$this->yyidx + - 1]->minor . ')';
    }

    #line 891 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r119()
    {
        $this->_retvalue = '(is_array($tmp = $_smarty_tpl->getConfigVariable( ' . $this->yystack[$this->yyidx + - 2]->minor . ')) ? $tmp' . $this->yystack[$this->yyidx + 0]->minor . ' : null)';
    }

    #line 895 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r120()
    {
        $this->_retvalue = array('var'                   => '\'' . substr($this->yystack[$this->yyidx + - 1]->minor, 1) . '\'',
                                 'smarty_internal_index' => $this->yystack[$this->yyidx + 0]->minor);
    }

    #line 898 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r121()
    {
        $this->_retvalue = array('var'                   => $this->yystack[$this->yyidx + - 1]->minor,
                                 'smarty_internal_index' => $this->yystack[$this->yyidx + 0]->minor);
    }

    #line 911 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r123()
    {
        return;
    }

    #line 917 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r124()
    {
        $this->_retvalue = '[' . $this->compiler->compileVariable('\'' . substr($this->yystack[$this->yyidx + 0]->minor, 1) . '\'') . ']';
    }

    #line 920 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r125()
    {
        $this->_retvalue = '[' . $this->compiler->compileVariable($this->yystack[$this->yyidx + 0]->minor) . ']';
    }

    #line 924 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r126()
    {
        $this->_retvalue = '[' . $this->compiler->compileVariable($this->yystack[$this->yyidx + - 2]->minor) . '->' . $this->yystack[$this->yyidx + 0]->minor . ']';
    }

    #line 928 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r127()
    {
        if (defined($this->yystack[$this->yyidx + 0]->minor)) {
            if ($this->security) {
                $this->security->isTrustedConstant($this->yystack[$this->yyidx + 0]->minor, $this->compiler);
            }
            $this->_retvalue = '[' . $this->yystack[$this->yyidx + 0]->minor . ']';
        } else {
            $this->_retvalue = "['" . $this->yystack[$this->yyidx + 0]->minor . "']";
        }
    }

    #line 939 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r128()
    {
        $this->_retvalue = '[' . $this->yystack[$this->yyidx + 0]->minor . ']';
    }

    #line 943 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r129()
    {
        $this->_retvalue = '[' . $this->yystack[$this->yyidx + - 1]->minor . ']';
    }

    #line 948 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r130()
    {
        $this->_retvalue = '[' . $this->compiler->compileTag('private_special_variable', array(), '[\'section\'][\'' . $this->yystack[$this->yyidx + - 1]->minor . '\'][\'index\']') . ']';
    }

    #line 952 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r131()
    {
        $this->_retvalue = '[' . $this->compiler->compileTag('private_special_variable', array(), '[\'section\'][\'' . $this->yystack[$this->yyidx + - 3]->minor . '\'][\'' . $this->yystack[$this->yyidx + - 1]->minor . '\']') . ']';
    }

    #line 955 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r132()
    {
        $this->_retvalue = '[' . $this->yystack[$this->yyidx + - 1]->minor . ']';
    }

    #line 961 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r134()
    {
        $this->_retvalue = '[' . $this->compiler->compileVariable('\'' . substr($this->yystack[$this->yyidx + - 1]->minor, 1) . '\'') . ']';;
    }

    #line 977 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r138()
    {
        $this->_retvalue = '[]';
    }

    #line 987 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r139()
    {
        $this->_retvalue = '\'' . substr($this->yystack[$this->yyidx + 0]->minor, 1) . '\'';
    }

    #line 991 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r140()
    {
        $this->_retvalue = "''";
    }

    #line 996 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r141()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 1]->minor . '.' . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 1006 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r143()
    {
        $this->_retvalue = '(' . $this->yystack[$this->yyidx + - 1]->minor . ')';
    }

    #line 1013 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r144()
    {
        if ($this->yystack[$this->yyidx + - 1]->minor['var'] == '\'smarty\'') {
            $this->_retvalue = $this->compiler->compileTag('private_special_variable', array(), $this->yystack[$this->yyidx + - 1]->minor['smarty_internal_index']) . $this->yystack[$this->yyidx + 0]->minor;
        } else {
            $this->_retvalue = $this->compiler->compileVariable($this->yystack[$this->yyidx + - 1]->minor['var']) . $this->yystack[$this->yyidx + - 1]->minor['smarty_internal_index'] . $this->yystack[$this->yyidx + 0]->minor;
        }
    }

    #line 1022 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r145()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 1027 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r146()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 1]->minor . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 1032 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r147()
    {
        if ($this->security && substr($this->yystack[$this->yyidx + - 1]->minor, 0, 1) == '_') {
            $this->compiler->trigger_template_error(self::Err1);
        }
        $this->_retvalue = '->' . $this->yystack[$this->yyidx + - 1]->minor . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 1039 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r148()
    {
        if ($this->security) {
            $this->compiler->trigger_template_error(self::Err2);
        }
        $this->_retvalue = '->{' . $this->compiler->compileVariable($this->yystack[$this->yyidx + - 1]->minor) . $this->yystack[$this->yyidx + 0]->minor . '}';
    }

    #line 1046 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r149()
    {
        if ($this->security) {
            $this->compiler->trigger_template_error(self::Err2);
        }
        $this->_retvalue = '->{' . $this->yystack[$this->yyidx + - 2]->minor . $this->yystack[$this->yyidx + 0]->minor . '}';
    }

    #line 1053 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r150()
    {
        if ($this->security) {
            $this->compiler->trigger_template_error(self::Err2);
        }
        $this->_retvalue = '->{\'' . $this->yystack[$this->yyidx + - 4]->minor . '\'.' . $this->yystack[$this->yyidx + - 2]->minor . $this->yystack[$this->yyidx + 0]->minor . '}';
    }

    #line 1061 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r151()
    {
        $this->_retvalue = '->' . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 1069 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r152()
    {
        if (!$this->security || $this->security->isTrustedPhpFunction($this->yystack[$this->yyidx + - 3]->minor, $this->compiler)) {
            if (strcasecmp($this->yystack[$this->yyidx + - 3]->minor, 'isset') === 0 || strcasecmp($this->yystack[$this->yyidx + - 3]->minor, 'empty') === 0 || strcasecmp($this->yystack[$this->yyidx + - 3]->minor, 'array') === 0 || is_callable($this->yystack[$this->yyidx + - 3]->minor)) {
                $func_name = strtolower($this->yystack[$this->yyidx + - 3]->minor);
                if ($func_name == 'isset') {
                    if (count($this->yystack[$this->yyidx + - 1]->minor) == 0) {
                        $this->compiler->trigger_template_error('Illegal number of paramer in "isset()"');
                    }
                    $par = implode(',', $this->yystack[$this->yyidx + - 1]->minor);
                    if (strncasecmp($par, '$_smarty_tpl->getConfigVariable', strlen('$_smarty_tpl->getConfigVariable')) === 0) {
                        self::$prefix_number ++;
                        $this->compiler->prefix_code[] = '<?php $_tmp' . self::$prefix_number . '=' . str_replace(')', ', false)', $par) . ';?>';
                        $isset_par = '$_tmp' . self::$prefix_number;
                    } else {
                        $isset_par = str_replace("')->value", "',null,true,false)->value", $par);
                    }
                    $this->_retvalue = $this->yystack[$this->yyidx + - 3]->minor . "(" . $isset_par . ")";
                } elseif (in_array($func_name, array('empty', 'reset', 'current', 'end', 'prev', 'next'))) {
                    if (count($this->yystack[$this->yyidx + - 1]->minor) != 1) {
                        $this->compiler->trigger_template_error('Illegal number of paramer in "empty()"');
                    }
                    if ($func_name == 'empty') {
                        $this->_retvalue = $func_name . '(' . str_replace("')->value", "',null,true,false)->value", $this->yystack[$this->yyidx + - 1]->minor[0]) . ')';
                    } else {
                        $this->_retvalue = $func_name . '(' . $this->yystack[$this->yyidx + - 1]->minor[0] . ')';
                    }
                } else {
                    $this->_retvalue = $this->yystack[$this->yyidx + - 3]->minor . "(" . implode(',', $this->yystack[$this->yyidx + - 1]->minor) . ")";
                }
            } else {
                $this->compiler->trigger_template_error("unknown function \"" . $this->yystack[$this->yyidx + - 3]->minor . "\"");
            }
        }
    }

    #line 1108 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r153()
    {
        if ($this->security && substr($this->yystack[$this->yyidx + - 3]->minor, 0, 1) == '_') {
            $this->compiler->trigger_template_error(self::Err1);
        }
        $this->_retvalue = $this->yystack[$this->yyidx + - 3]->minor . "(" . implode(',', $this->yystack[$this->yyidx + - 1]->minor) . ")";
    }

    #line 1115 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r154()
    {
        if ($this->security) {
            $this->compiler->trigger_template_error(self::Err2);
        }
        self::$prefix_number ++;
        $this->compiler->prefix_code[] = '<?php $_tmp' . self::$prefix_number . '=' . $this->compiler->compileVariable('\'' . substr($this->yystack[$this->yyidx + - 3]->minor, 1) . '\'') . ';?>';
        $this->_retvalue = '$_tmp' . self::$prefix_number . '(' . implode(',', $this->yystack[$this->yyidx + - 1]->minor) . ')';
    }

    #line 1126 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r155()
    {
        $this->_retvalue = array_merge($this->yystack[$this->yyidx + - 2]->minor, array($this->yystack[$this->yyidx + 0]->minor));
    }

    #line 1143 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r158()
    {
        $this->_retvalue = array_merge($this->yystack[$this->yyidx + - 2]->minor, array(array_merge($this->yystack[$this->yyidx + - 1]->minor, $this->yystack[$this->yyidx + 0]->minor)));
    }

    #line 1147 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r159()
    {
        $this->_retvalue = array(array_merge($this->yystack[$this->yyidx + - 1]->minor, $this->yystack[$this->yyidx + 0]->minor));
    }

    #line 1155 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r161()
    {
        $this->_retvalue = array($this->yystack[$this->yyidx + 0]->minor);
    }

    #line 1163 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r162()
    {
        $this->_retvalue = array_merge($this->yystack[$this->yyidx + - 1]->minor, $this->yystack[$this->yyidx + 0]->minor);
    }

    #line 1182 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r166()
    {
        $this->_retvalue = array($this->yystack[$this->yyidx + 0]->minor, '', 'method');
    }

    #line 1187 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r167()
    {
        $this->_retvalue = array($this->yystack[$this->yyidx + - 1]->minor, $this->yystack[$this->yyidx + 0]->minor,
            'method');
    }

    #line 1192 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r168()
    {
        $this->_retvalue = array($this->yystack[$this->yyidx + 0]->minor, '');
    }

    #line 1197 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r169()
    {
        $this->_retvalue = array($this->yystack[$this->yyidx + - 1]->minor, $this->yystack[$this->yyidx + 0]->minor,
            'property');
    }

    #line 1202 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r170()
    {
        $this->_retvalue = array($this->yystack[$this->yyidx + - 2]->minor,
            $this->yystack[$this->yyidx + - 1]->minor . $this->yystack[$this->yyidx + 0]->minor, 'property');
    }

    #line 1208 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r171()
    {
        $this->_retvalue['op'] = ' ' . trim($this->yystack[$this->yyidx + 0]->minor) . ' ';
    }

    #line 1212 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r172()
    {
        static $lops = array('eq'          => array('op' => ' == ', 'pre' => null),
                             'ne'          => array('op' => ' != ', 'pre' => null),
                             'neq'         => array('op' => ' != ', 'pre' => null),
                             'gt'          => array('op' => ' > ', 'pre' => null),
                             'ge'          => array('op' => ' >= ', 'pre' => null),
                             'gte'         => array('op' => ' >= ', 'pre' => null),
                             'lt'          => array('op' => ' < ', 'pre' => null),
                             'le'          => array('op' => ' <= ', 'pre' => null),
                             'lte'         => array('op' => ' <= ', 'pre' => null),
                             'mod'         => array('op' => ' % ', 'pre' => null),
                             'and'         => array('op' => ' && ', 'pre' => null),
                             'or'          => array('op' => ' || ', 'pre' => null),
                             'xor'         => array('op' => ' xor ', 'pre' => null),
                             'isdivby'     => array('op' => ' % ', 'pre' => '!('),
                             'isnotdivby'  => array('op' => ' % ', 'pre' => '('),
                             'isevenby'    => array('op' => ' / ', 'pre' => '!(1 & '),
                             'isnotevenby' => array('op' => ' / ', 'pre' => '(1 & '),
                             'isoddby'     => array('op' => ' / ', 'pre' => '(1 & '),
                             'isnotoddby'  => array('op' => ' / ', 'pre' => '!(1 & '),);
        $op = strtolower(preg_replace('/\s*/', '', $this->yystack[$this->yyidx + 0]->minor));
        $this->_retvalue = $lops[$op];
    }

    #line 1238 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r173()
    {
        static $scond = array('iseven'   => '!(1 & ', 'isnoteven' => '(1 & ', 'isodd' => '(1 & ',
                              'isnotodd' => '!(1 & ',);
        $op = strtolower(str_replace(' ', '', $this->yystack[$this->yyidx + 0]->minor));
        $this->_retvalue = $scond[$op];
    }

    #line 1252 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r174()
    {
        $this->_retvalue = 'array(' . $this->yystack[$this->yyidx + - 1]->minor . ')';
    }

    #line 1260 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r176()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 2]->minor . ',' . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 1268 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r178()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 2]->minor . '=>' . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 1272 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r179()
    {
        $this->_retvalue = '\'' . $this->yystack[$this->yyidx + - 2]->minor . '\'=>' . $this->yystack[$this->yyidx + 0]->minor;
    }

    #line 1288 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r182()
    {
        $this->_retvalue = $this->yystack[$this->yyidx + - 1]->minor->to_smarty_php();
    }

    #line 1293 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r183()
    {
        $this->yystack[$this->yyidx + - 1]->minor->append_subtree($this->yystack[$this->yyidx + 0]->minor);
        $this->_retvalue = $this->yystack[$this->yyidx + - 1]->minor;
    }

    #line 1298 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r184()
    {
        $this->_retvalue = new Smarty_Internal_ParseTree_Dq($this, $this->yystack[$this->yyidx + 0]->minor);
    }

    #line 1302 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r185()
    {
        $this->_retvalue = new Smarty_Internal_ParseTree_Code($this, '(string)' . $this->yystack[$this->yyidx + - 1]->minor);
    }

    #line 1310 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r187()
    {
        $this->_retvalue = new Smarty_Internal_ParseTree_Code($this, '(string)$_smarty_tpl->tpl_vars[\'' . substr($this->yystack[$this->yyidx + 0]->minor, 1) . '\']->value');
    }

    #line 1318 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r189()
    {
        $this->_retvalue = new Smarty_Internal_ParseTree_Code($this, '(string)(' . $this->yystack[$this->yyidx + - 1]->minor . ')');
    }

    #line 1322 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r190()
    {
        $this->_retvalue = new Smarty_Internal_ParseTree_Tag($this, $this->yystack[$this->yyidx + 0]->minor);
    }

    #line 1326 "../smarty/lexer/smarty_internal_templateparser.y"
    function yy_r191()
    {
        $this->_retvalue = new Smarty_Internal_ParseTree_DqContent($this, $this->yystack[$this->yyidx + 0]->minor);
    }

    private $_retvalue;

    public function yy_reduce($yyruleno)
    {
        if ($this->yyTraceFILE && $yyruleno >= 0 && $yyruleno < count(self::$yyRuleName)) {
            fprintf($this->yyTraceFILE, "%sReduce (%d) [%s].\n", $this->yyTracePrompt, $yyruleno, self::$yyRuleName[$yyruleno]);
        }

        $this->_retvalue = $yy_lefthand_side = null;
        if (isset(self::$yyReduceMap[$yyruleno])) {
            // call the action
            $this->_retvalue = null;
            $this->{'yy_r' . self::$yyReduceMap[$yyruleno]}();
            $yy_lefthand_side = $this->_retvalue;
        }
        $yygoto = self::$yyRuleInfo[$yyruleno][0];
        $yysize = self::$yyRuleInfo[$yyruleno][1];
        $this->yyidx -= $yysize;
        for ($i = $yysize; $i; $i --) {
            // pop all of the right-hand side parameters
            array_pop($this->yystack);
        }
        $yyact = $this->yy_find_reduce_action($this->yystack[$this->yyidx]->stateno, $yygoto);
        if ($yyact < self::YYNSTATE) {
            if (!$this->yyTraceFILE && $yysize) {
                $this->yyidx ++;
                $x = new TP_yyStackEntry;
                $x->stateno = $yyact;
                $x->major = $yygoto;
                $x->minor = $yy_lefthand_side;
                $this->yystack[$this->yyidx] = $x;
            } else {
                $this->yy_shift($yyact, $yygoto, $yy_lefthand_side);
            }
        } elseif ($yyact == self::YYNSTATE + self::YYNRULE + 1) {
            $this->yy_accept();
        }
    }

    public function yy_parse_failed()
    {
        if ($this->yyTraceFILE) {
            fprintf($this->yyTraceFILE, "%sFail!\n", $this->yyTracePrompt);
        }
        while ($this->yyidx >= 0) {
            $this->yy_pop_parser_stack();
        }
    }

    public function yy_syntax_error($yymajor, $TOKEN)
    {
        #line 183 "../smarty/lexer/smarty_internal_templateparser.y"

        $this->internalError = true;
        $this->yymajor = $yymajor;
        $this->compiler->trigger_template_error();
    }

    public function yy_accept()
    {
        if ($this->yyTraceFILE) {
            fprintf($this->yyTraceFILE, "%sAccept!\n", $this->yyTracePrompt);
        }
        while ($this->yyidx >= 0) {
            $this->yy_pop_parser_stack();
        }
        #line 176 "../smarty/lexer/smarty_internal_templateparser.y"

        $this->successful = !$this->internalError;
        $this->internalError = false;
        $this->retvalue = $this->_retvalue;
    }

    public function doParse($yymajor, $yytokenvalue)
    {
        $yyerrorhit = 0;   /* True if yymajor has invoked an error */

        if ($this->yyidx === null || $this->yyidx < 0) {
            $this->yyidx = 0;
            $this->yyerrcnt = - 1;
            $x = new TP_yyStackEntry;
            $x->stateno = 0;
            $x->major = 0;
            $this->yystack = array();
            $this->yystack[] = $x;
        }
        $yyendofinput = ($yymajor == 0);

        if ($this->yyTraceFILE) {
            fprintf($this->yyTraceFILE, "%sInput %s\n", $this->yyTracePrompt, $this->yyTokenName[$yymajor]);
        }

        do {
            $yyact = $this->yy_find_shift_action($yymajor);
            if ($yymajor < self::YYERRORSYMBOL && !$this->yy_is_expected_token($yymajor)) {
                // force a syntax error
                $yyact = self::YY_ERROR_ACTION;
            }
            if ($yyact < self::YYNSTATE) {
                $this->yy_shift($yyact, $yymajor, $yytokenvalue);
                $this->yyerrcnt --;
                if ($yyendofinput && $this->yyidx >= 0) {
                    $yymajor = 0;
                } else {
                    $yymajor = self::YYNOCODE;
                }
            } elseif ($yyact < self::YYNSTATE + self::YYNRULE) {
                $this->yy_reduce($yyact - self::YYNSTATE);
            } elseif ($yyact == self::YY_ERROR_ACTION) {
                if ($this->yyTraceFILE) {
                    fprintf($this->yyTraceFILE, "%sSyntax Error!\n", $this->yyTracePrompt);
                }
                if (self::YYERRORSYMBOL) {
                    if ($this->yyerrcnt < 0) {
                        $this->yy_syntax_error($yymajor, $yytokenvalue);
                    }
                    $yymx = $this->yystack[$this->yyidx]->major;
                    if ($yymx == self::YYERRORSYMBOL || $yyerrorhit) {
                        if ($this->yyTraceFILE) {
                            fprintf($this->yyTraceFILE, "%sDiscard input token %s\n", $this->yyTracePrompt, $this->yyTokenName[$yymajor]);
                        }
                        $this->yy_destructor($yymajor, $yytokenvalue);
                        $yymajor = self::YYNOCODE;
                    } else {
                        while ($this->yyidx >= 0 && $yymx != self::YYERRORSYMBOL && ($yyact = $this->yy_find_shift_action(self::YYERRORSYMBOL)) >= self::YYNSTATE) {
                            $this->yy_pop_parser_stack();
                        }
                        if ($this->yyidx < 0 || $yymajor == 0) {
                            $this->yy_destructor($yymajor, $yytokenvalue);
                            $this->yy_parse_failed();
                            $yymajor = self::YYNOCODE;
                        } elseif ($yymx != self::YYERRORSYMBOL) {
                            $u2 = 0;
                            $this->yy_shift($yyact, self::YYERRORSYMBOL, $u2);
                        }
                    }
                    $this->yyerrcnt = 3;
                    $yyerrorhit = 1;
                } else {
                    if ($this->yyerrcnt <= 0) {
                        $this->yy_syntax_error($yymajor, $yytokenvalue);
                    }
                    $this->yyerrcnt = 3;
                    $this->yy_destructor($yymajor, $yytokenvalue);
                    if ($yyendofinput) {
                        $this->yy_parse_failed();
                    }
                    $yymajor = self::YYNOCODE;
                }
            } else {
                $this->yy_accept();
                $yymajor = self::YYNOCODE;
            }
        } while ($yymajor != self::YYNOCODE && $this->yyidx >= 0);
    }
}

